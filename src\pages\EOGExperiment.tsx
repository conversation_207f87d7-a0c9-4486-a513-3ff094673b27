import React from 'react';
import { Eye } from 'lucide-react';

const EOGExperiment = () => {
  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div className="flex items-center space-x-4 mb-6">
          <div className="p-3 bg-blue-100 rounded-xl">
            <Eye className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">EOG - Electrooculogram</h1>
            <p className="text-lg text-gray-600">Eye movement tracking system</p>
          </div>
        </div>
        
        <div className="bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">Coming Soon</h3>
          <p className="text-blue-700">
            Complete EOG experiment documentation including dual-channel recording 
            for horizontal and vertical eye movements, blink detection, and 
            electrode placement around the eyes will be available soon.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EOGExperiment;