<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Lab Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار مختبر ECG</h1>
        <div class="status" id="status">جاري التحميل...</div>
        
        <h2>اختبار الأزرار</h2>
        <button class="btn" onclick="testFunction()">اختبار الوظيفة</button>
        <button class="btn" onclick="testCanvas()">اختبار Canvas</button>
        
        <h2>اختبار Canvas</h2>
        <canvas id="testCanvas" width="400" height="200"></canvas>
        
        <h2>اختبار تحميل الملفات</h2>
        <div id="fileStatus"></div>
    </div>

    <script>
        // Test basic functionality
        function testFunction() {
            document.getElementById('status').textContent = 'تم اختبار الوظيفة بنجاح!';
            document.getElementById('status').style.background = '#d1ecf1';
            document.getElementById('status').style.color = '#0c5460';
        }

        function testCanvas() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw test ECG-like wave
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let x = 0; x < canvas.width; x++) {
                const y = canvas.height / 2 + Math.sin(x * 0.1) * 30 + Math.sin(x * 0.05) * 10;
                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
            
            document.getElementById('status').textContent = 'تم اختبار Canvas بنجاح!';
            document.getElementById('status').style.background = '#d4edda';
            document.getElementById('status').style.color = '#155724';
        }

        // Test file loading
        function checkFiles() {
            const files = [
                'css/styles.css',
                'css/animations.css', 
                'js/ecg-simulation.js',
                'js/circuit-diagrams.js',
                'js/measurements.js',
                'js/main.js'
            ];
            
            const fileStatus = document.getElementById('fileStatus');
            fileStatus.innerHTML = '<h3>حالة الملفات:</h3>';
            
            files.forEach(file => {
                const div = document.createElement('div');
                div.style.margin = '5px 0';
                
                // Try to load file
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            div.innerHTML = `✅ ${file} - تم التحميل`;
                            div.style.color = 'green';
                        } else {
                            div.innerHTML = `❌ ${file} - خطأ في التحميل (${response.status})`;
                            div.style.color = 'red';
                        }
                    })
                    .catch(error => {
                        div.innerHTML = `❌ ${file} - غير موجود`;
                        div.style.color = 'red';
                    });
                
                fileStatus.appendChild(div);
            });
        }

        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('status').textContent = 'تم تحميل الصفحة بنجاح!';
            checkFiles();
            
            // Test canvas immediately
            setTimeout(testCanvas, 1000);
        });
    </script>
</body>
</html>
