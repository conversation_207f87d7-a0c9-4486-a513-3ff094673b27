// Circuit Diagrams and Interactive Components
class CircuitDiagram {
    constructor() {
        this.currentCircuit = 'complete';
        this.zoomLevel = 1;
        this.components = this.initializeComponents();
        this.setupEventListeners();
        this.drawCompleteCircuit();
    }

    initializeComponents() {
        return {
            complete: [
                { id: 'U1', type: 'IC', name: 'AD623', description: 'مضخم أجهزة', x: 200, y: 150, pins: 8 },
                { id: 'U2A', type: 'IC', name: 'MCP6002', description: 'مضخم عمليات', x: 400, y: 200, pins: 8 },
                { id: 'R1', type: 'resistor', name: '10kΩ', description: 'مقاومة مقسم الجهد', x: 100, y: 100 },
                { id: 'R2', type: 'resistor', name: '10kΩ', description: 'مقاومة مقسم الجهد', x: 100, y: 180 },
                { id: 'Rg', type: 'resistor', name: '1kΩ', description: 'مقاومة الكسب', x: 150, y: 120 },
                { id: 'C1', type: 'capacitor', name: '1µF', description: 'مكثف HPF', x: 300, y: 150 },
                { id: 'C2', type: 'capacitor', name: '0.1µF', description: 'مكثف LPF', x: 500, y: 150 }
            ],
            amplifier: [
                { id: 'U1', type: 'IC', name: 'AD623', description: 'مضخم أجهزة', x: 300, y: 200, pins: 8 },
                { id: 'Rg', type: 'resistor', name: '1kΩ', description: 'مقاومة الكسب', x: 250, y: 150 }
            ],
            filter: [
                { id: 'R_hpf', type: 'resistor', name: '330kΩ', description: 'مقاومة HPF', x: 200, y: 150 },
                { id: 'C_hpf', type: 'capacitor', name: '1µF', description: 'مكثف HPF', x: 300, y: 150 },
                { id: 'R_lpf', type: 'resistor', name: '10kΩ', description: 'مقاومة LPF', x: 400, y: 150 },
                { id: 'C_lpf', type: 'capacitor', name: '0.1µF', description: 'مكثف LPF', x: 500, y: 150 }
            ],
            reference: [
                { id: 'U2A', type: 'IC', name: 'MCP6002', description: 'مخزن مؤقت', x: 300, y: 200, pins: 8 },
                { id: 'R1', type: 'resistor', name: '10kΩ', description: 'مقاومة مقسم الجهد', x: 200, y: 150 },
                { id: 'R2', type: 'resistor', name: '10kΩ', description: 'مقاومة مقسم الجهد', x: 200, y: 250 }
            ]
        };
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const circuit = e.target.getAttribute('onclick').match(/'([^']+)'/)[1];
                this.showCircuit(circuit);
            });
        });
    }

    showCircuit(circuitType) {
        this.currentCircuit = circuitType;
        
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        
        // Clear and redraw
        this.clearSVG();
        
        switch(circuitType) {
            case 'complete':
                this.drawCompleteCircuit();
                break;
            case 'amplifier':
                this.drawAmplifierCircuit();
                break;
            case 'filter':
                this.drawFilterCircuit();
                break;
            case 'reference':
                this.drawReferenceCircuit();
                break;
        }
        
        this.updateComponentsList();
    }

    clearSVG() {
        const svg = document.getElementById('completeDiagram');
        if (svg) {
            svg.innerHTML = '';
        }
    }

    drawCompleteCircuit() {
        const svg = document.getElementById('completeDiagram');
        if (!svg) return;

        // Define circuit elements
        const elements = `
            <!-- Power Supply -->
            <text x="50" y="30" fill="#333" font-size="12" font-weight="bold">Vcc = 5V</text>
            <line x1="50" y1="40" x2="750" y2="40" stroke="#ff0000" stroke-width="2"/>
            
            <!-- Ground -->
            <text x="50" y="580" fill="#333" font-size="12" font-weight="bold">GND</text>
            <line x1="50" y1="560" x2="750" y2="560" stroke="#000" stroke-width="2"/>
            
            <!-- Input Electrodes -->
            <circle cx="80" cy="200" r="8" fill="#4CAF50" class="circuit-component" data-info="LA - Left Arm Electrode"/>
            <text x="60" y="190" fill="#333" font-size="10">LA</text>
            
            <circle cx="80" cy="250" r="8" fill="#4CAF50" class="circuit-component" data-info="RA - Right Arm Electrode"/>
            <text x="60" y="240" fill="#333" font-size="10">RA</text>
            
            <circle cx="80" cy="300" r="8" fill="#4CAF50" class="circuit-component" data-info="RL - Right Leg (Reference)"/>
            <text x="60" y="290" fill="#333" font-size="10">RL</text>
            
            <!-- Reference Circuit -->
            <rect x="120" y="100" width="80" height="60" fill="none" stroke="#333" stroke-width="1" class="circuit-component" data-info="Voltage Divider R1=R2=10kΩ"/>
            <text x="140" y="125" fill="#333" font-size="10">R1</text>
            <text x="140" y="145" fill="#333" font-size="10">10kΩ</text>
            
            <rect x="120" y="180" width="80" height="60" fill="none" stroke="#333" stroke-width="1" class="circuit-component" data-info="Voltage Divider R1=R2=10kΩ"/>
            <text x="140" y="205" fill="#333" font-size="10">R2</text>
            <text x="140" y="225" fill="#333" font-size="10">10kΩ</text>
            
            <!-- Buffer Op-Amp -->
            <polygon points="220,150 220,200 270,175" fill="none" stroke="#333" stroke-width="2" class="circuit-component" data-info="MCP6002 Buffer (U2A)"/>
            <text x="235" y="170" fill="#333" font-size="8">U2A</text>
            <text x="225" y="185" fill="#333" font-size="8">+</text>
            <text x="225" y="165" fill="#333" font-size="8">-</text>
            
            <!-- Instrumentation Amplifier -->
            <rect x="320" y="150" width="100" height="80" fill="none" stroke="#333" stroke-width="2" class="circuit-component" data-info="AD623 Instrumentation Amplifier"/>
            <text x="350" y="175" fill="#333" font-size="12" font-weight="bold">AD623</text>
            <text x="360" y="190" fill="#333" font-size="10">U1</text>
            <text x="345" y="205" fill="#333" font-size="8">Gain=100</text>
            
            <!-- Gain Resistor -->
            <rect x="340" y="120" width="60" height="20" fill="none" stroke="#333" stroke-width="1" class="circuit-component" data-info="Gain Setting Resistor Rg=1kΩ"/>
            <text x="360" y="135" fill="#333" font-size="10">Rg=1kΩ</text>
            
            <!-- High Pass Filter -->
            <rect x="480" y="170" width="60" height="20" fill="none" stroke="#333" stroke-width="1" class="circuit-component" data-info="HPF Resistor 330kΩ"/>
            <text x="495" y="185" fill="#333" font-size="8">330kΩ</text>
            
            <rect x="560" y="160" width="20" height="40" fill="none" stroke="#333" stroke-width="1" class="circuit-component" data-info="HPF Capacitor 1µF"/>
            <text x="565" y="175" fill="#333" font-size="8">1µF</text>
            
            <!-- Low Pass Filter -->
            <rect x="620" y="170" width="60" height="20" fill="none" stroke="#333" stroke-width="1" class="circuit-component" data-info="LPF Resistor 10kΩ"/>
            <text x="635" y="185" fill="#333" font-size="8">10kΩ</text>
            
            <rect x="700" y="160" width="20" height="40" fill="none" stroke="#333" stroke-width="1" class="circuit-component" data-info="LPF Capacitor 0.1µF"/>
            <text x="700" y="175" fill="#333" font-size="8">0.1µF</text>
            
            <!-- Connections -->
            <line x1="88" y1="200" x2="320" y2="200" stroke="#333" stroke-width="1" class="circuit-wire"/>
            <line x1="88" y1="250" x2="320" y2="220" stroke="#333" stroke-width="1" class="circuit-wire"/>
            <line x1="88" y1="300" x2="270" y2="300" stroke="#333" stroke-width="1" class="circuit-wire"/>
            
            <line x1="160" y1="130" x2="160" y2="40" stroke="#ff0000" stroke-width="1"/>
            <line x1="160" y1="240" x2="160" y2="560" stroke="#000" stroke-width="1"/>
            <line x1="200" y1="175" x2="220" y2="175" stroke="#333" stroke-width="1" class="circuit-wire"/>
            <line x1="270" y1="175" x2="320" y2="175" stroke="#333" stroke-width="1" class="circuit-wire"/>
            
            <line x1="420" y1="190" x2="480" y2="190" stroke="#333" stroke-width="1" class="circuit-wire"/>
            <line x1="540" y1="190" x2="560" y2="190" stroke="#333" stroke-width="1" class="circuit-wire"/>
            <line x1="580" y1="190" x2="620" y2="190" stroke="#333" stroke-width="1" class="circuit-wire"/>
            <line x1="680" y1="190" x2="700" y2="190" stroke="#333" stroke-width="1" class="circuit-wire"/>
            
            <!-- Output -->
            <circle cx="750" cy="190" r="8" fill="#FF5722" class="circuit-component" data-info="ECG Output Signal"/>
            <text x="730" y="180" fill="#333" font-size="10">Output</text>
            <line x1="720" y1="190" x2="742" y2="190" stroke="#333" stroke-width="2" class="circuit-wire"/>
        `;

        svg.innerHTML = elements;
        this.addInteractivity();
    }

    drawAmplifierCircuit() {
        const svg = document.getElementById('completeDiagram');
        if (!svg) return;

        const elements = `
            <!-- AD623 Instrumentation Amplifier -->
            <rect x="300" y="200" width="120" height="100" fill="none" stroke="#333" stroke-width="3" class="circuit-component"/>
            <text x="340" y="230" fill="#333" font-size="16" font-weight="bold">AD623</text>
            <text x="350" y="250" fill="#333" font-size="12">U1</text>
            
            <!-- Input pins -->
            <circle cx="280" cy="220" r="4" fill="#4CAF50"/>
            <text x="250" y="225" fill="#333" font-size="12">+IN (LA)</text>
            <line x1="200" y1="220" x2="280" y2="220" stroke="#333" stroke-width="2"/>
            
            <circle cx="280" cy="260" r="4" fill="#4CAF50"/>
            <text x="250" y="265" fill="#333" font-size="12">-IN (RA)</text>
            <line x1="200" y1="260" x2="280" y2="260" stroke="#333" stroke-width="2"/>
            
            <!-- Gain resistor -->
            <rect x="320" y="160" width="80" height="25" fill="none" stroke="#333" stroke-width="2" class="circuit-component"/>
            <text x="345" y="178" fill="#333" font-size="12">Rg = 1kΩ</text>
            <line x1="340" y1="185" x2="340" y2="200" stroke="#333" stroke-width="1"/>
            <line x1="380" y1="185" x2="380" y2="200" stroke="#333" stroke-width="1"/>
            
            <!-- Reference pin -->
            <circle cx="360" cy="320" r="4" fill="#FF9800"/>
            <text x="320" y="340" fill="#333" font-size="12">REF (Vcc/2)</text>
            <line x1="360" y1="300" x2="360" y2="320" stroke="#333" stroke-width="2"/>
            
            <!-- Output -->
            <circle cx="440" cy="240" r="4" fill="#FF5722"/>
            <text x="450" y="245" fill="#333" font-size="12">OUTPUT</text>
            <line x1="420" y1="240" x2="440" y2="240" stroke="#333" stroke-width="2"/>
            
            <!-- Power supply -->
            <text x="350" y="120" fill="#333" font-size="12">+Vs (Vcc)</text>
            <line x1="360" y1="130" x2="360" y2="200" stroke="#ff0000" stroke-width="2"/>
            
            <text x="350" y="380" fill="#333" font-size="12">-Vs (GND)</text>
            <line x1="360" y1="300" x2="360" y2="370" stroke="#000" stroke-width="2"/>
        `;

        svg.innerHTML = elements;
        this.addInteractivity();
    }

    drawFilterCircuit() {
        const svg = document.getElementById('completeDiagram');
        if (!svg) return;

        const elements = `
            <!-- High Pass Filter -->
            <text x="150" y="100" fill="#333" font-size="14" font-weight="bold">High Pass Filter</text>
            <rect x="150" y="150" width="80" height="25" fill="none" stroke="#333" stroke-width="2" class="circuit-component"/>
            <text x="175" y="168" fill="#333" font-size="12">R = 330kΩ</text>
            
            <rect x="280" y="140" width="25" height="45" fill="none" stroke="#333" stroke-width="2" class="circuit-component"/>
            <text x="285" y="158" fill="#333" font-size="10">C</text>
            <text x="280" y="175" fill="#333" font-size="10">1µF</text>
            
            <line x1="100" y1="162" x2="150" y2="162" stroke="#333" stroke-width="2"/>
            <line x1="230" y1="162" x2="280" y2="162" stroke="#333" stroke-width="2"/>
            <line x1="305" y1="162" x2="350" y2="162" stroke="#333" stroke-width="2"/>
            <line x1="292" y1="185" x2="292" y2="220" stroke="#000" stroke-width="2"/>
            
            <!-- Low Pass Filter -->
            <text x="450" y="100" fill="#333" font-size="14" font-weight="bold">Low Pass Filter</text>
            <rect x="450" y="150" width="80" height="25" fill="none" stroke="#333" stroke-width="2" class="circuit-component"/>
            <text x="475" y="168" fill="#333" font-size="12">R = 10kΩ</text>
            
            <rect x="580" y="140" width="25" height="45" fill="none" stroke="#333" stroke-width="2" class="circuit-component"/>
            <text x="585" y="158" fill="#333" font-size="10">C</text>
            <text x="575" y="175" fill="#333" font-size="10">0.1µF</text>
            
            <line x1="350" y1="162" x2="450" y2="162" stroke="#333" stroke-width="2"/>
            <line x1="530" y1="162" x2="580" y2="162" stroke="#333" stroke-width="2"/>
            <line x1="605" y1="162" x2="650" y2="162" stroke="#333" stroke-width="2"/>
            <line x1="592" y1="185" x2="592" y2="220" stroke="#000" stroke-width="2"/>
            
            <!-- Input/Output labels -->
            <text x="50" y="167" fill="#333" font-size="12">Input</text>
            <text x="660" y="167" fill="#333" font-size="12">Output</text>
            
            <!-- Frequency response -->
            <text x="200" y="250" fill="#666" font-size="10">fc = 0.48 Hz</text>
            <text x="500" y="250" fill="#666" font-size="10">fc = 159 Hz</text>
        `;

        svg.innerHTML = elements;
        this.addInteractivity();
    }

    drawReferenceCircuit() {
        const svg = document.getElementById('completeDiagram');
        if (!svg) return;

        const elements = `
            <!-- Voltage Divider -->
            <text x="200" y="80" fill="#333" font-size="14" font-weight="bold">Voltage Reference Circuit</text>
            
            <line x1="200" y1="100" x2="200" y2="120" stroke="#ff0000" stroke-width="3"/>
            <text x="210" y="110" fill="#333" font-size="12">Vcc = 5V</text>
            
            <rect x="180" y="120" width="40" height="60" fill="none" stroke="#333" stroke-width="2" class="circuit-component"/>
            <text x="190" y="145" fill="#333" font-size="10">R1</text>
            <text x="185" y="160" fill="#333" font-size="10">10kΩ</text>
            
            <rect x="180" y="200" width="40" height="60" fill="none" stroke="#333" stroke-width="2" class="circuit-component"/>
            <text x="190" y="225" fill="#333" font-size="10">R2</text>
            <text x="185" y="240" fill="#333" font-size="10">10kΩ</text>
            
            <line x1="200" y1="180" x2="200" y2="200" stroke="#333" stroke-width="2"/>
            <line x1="200" y1="260" x2="200" y2="280" stroke="#000" stroke-width="3"/>
            <text x="210" y="290" fill="#333" font-size="12">GND</text>
            
            <!-- Buffer Op-Amp -->
            <polygon points="300,160 300,220 370,190" fill="none" stroke="#333" stroke-width="3" class="circuit-component"/>
            <text x="320" y="185" fill="#333" font-size="12">U2A</text>
            <text x="310" y="175" fill="#333" font-size="12">+</text>
            <text x="310" y="205" fill="#333" font-size="12">-</text>
            
            <!-- Connections -->
            <line x1="200" y1="190" x2="300" y2="190" stroke="#333" stroke-width="2"/>
            <line x1="300" y1="200" x2="280" y2="200" stroke="#333" stroke-width="2"/>
            <line x1="280" y1="200" x2="280" y2="240" stroke="#333" stroke-width="2"/>
            <line x1="280" y1="240" x2="400" y2="240" stroke="#333" stroke-width="2"/>
            <line x1="400" y1="240" x2="400" y2="190" stroke="#333" stroke-width="2"/>
            <line x1="370" y1="190" x2="400" y2="190" stroke="#333" stroke-width="2"/>
            
            <!-- Output -->
            <circle cx="450" cy="190" r="6" fill="#FF5722"/>
            <text x="460" y="195" fill="#333" font-size="12">Vref = Vcc/2</text>
            <line x1="400" y1="190" x2="444" y2="190" stroke="#333" stroke-width="3"/>
            
            <!-- Voltage annotation -->
            <text x="250" y="195" fill="#666" font-size="10">2.5V</text>
        `;

        svg.innerHTML = elements;
        this.addInteractivity();
    }

    addInteractivity() {
        document.querySelectorAll('.circuit-component').forEach(component => {
            component.addEventListener('mouseenter', (e) => {
                e.target.style.filter = 'drop-shadow(0 0 10px rgba(37, 99, 235, 0.8))';
                e.target.style.transform = 'scale(1.05)';
                
                const info = e.target.getAttribute('data-info');
                if (info) {
                    this.showTooltip(e, info);
                }
            });
            
            component.addEventListener('mouseleave', (e) => {
                e.target.style.filter = '';
                e.target.style.transform = '';
                this.hideTooltip();
            });
        });
    }

    showTooltip(event, text) {
        let tooltip = document.getElementById('circuit-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'circuit-tooltip';
            tooltip.style.cssText = `
                position: absolute;
                background: rgba(0,0,0,0.9);
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 1000;
                pointer-events: none;
                max-width: 200px;
            `;
            document.body.appendChild(tooltip);
        }
        
        tooltip.textContent = text;
        tooltip.style.left = event.pageX + 10 + 'px';
        tooltip.style.top = event.pageY - 30 + 'px';
        tooltip.style.display = 'block';
    }

    hideTooltip() {
        const tooltip = document.getElementById('circuit-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }

    updateComponentsList() {
        const list = document.getElementById('componentsList');
        if (!list) return;
        
        const components = this.components[this.currentCircuit];
        list.innerHTML = components.map(comp => `
            <div class="component-item" style="margin-bottom: 10px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <strong>${comp.id}</strong> - ${comp.name}<br>
                <small style="color: #666;">${comp.description}</small>
            </div>
        `).join('');
    }

    zoomIn() {
        this.zoomLevel = Math.min(this.zoomLevel * 1.2, 3);
        this.applyZoom();
    }

    zoomOut() {
        this.zoomLevel = Math.max(this.zoomLevel / 1.2, 0.5);
        this.applyZoom();
    }

    applyZoom() {
        const svg = document.getElementById('completeDiagram');
        if (svg) {
            svg.style.transform = `scale(${this.zoomLevel})`;
        }
    }

    downloadDiagram() {
        const svg = document.getElementById('completeDiagram');
        if (!svg) return;
        
        const svgData = new XMLSerializer().serializeToString(svg);
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            const link = document.createElement('a');
            link.download = `circuit-${this.currentCircuit}-${new Date().toISOString().slice(0, 10)}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }.bind(this);
        
        img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
    }
}

// Global functions
function showCircuit(type) {
    if (window.circuitDiagram) {
        window.circuitDiagram.showCircuit(type);
    }
}

function zoomIn() {
    if (window.circuitDiagram) {
        window.circuitDiagram.zoomIn();
    }
}

function zoomOut() {
    if (window.circuitDiagram) {
        window.circuitDiagram.zoomOut();
    }
}

function downloadDiagram() {
    if (window.circuitDiagram) {
        window.circuitDiagram.downloadDiagram();
    }
}

function viewCircuits() {
    document.getElementById('circuits').scrollIntoView({ behavior: 'smooth' });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.circuitDiagram = new CircuitDiagram();
});
