import React from 'react';
import { Brain } from 'lucide-react';

const EEGExperiment = () => {
  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div className="flex items-center space-x-4 mb-6">
          <div className="p-3 bg-purple-100 rounded-xl">
            <Brain className="h-8 w-8 text-purple-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">EEG - Electroencephalogram</h1>
            <p className="text-lg text-gray-600">Brain electrical activity recording</p>
          </div>
        </div>
        
        <div className="bg-purple-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-purple-800 mb-2">Coming Soon</h3>
          <p className="text-purple-700">
            Complete EEG experiment documentation including low-noise amplification, 
            alpha rhythm detection, artifact filtering, and 10-20 electrode placement 
            system will be available soon.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EEGExperiment;