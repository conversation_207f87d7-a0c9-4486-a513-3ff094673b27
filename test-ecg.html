<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مختبر ECG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 50px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        #status {
            font-size: 18px;
            margin: 20px 0;
            padding: 10px;
            background: rgba(0,255,0,0.2);
            border-radius: 10px;
        }
        canvas {
            background: #001100;
            border: 2px solid #00ff00;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🫀 اختبار مختبر تخطيط كهربية القلب</h1>
        <div id="status">جاهز للاختبار</div>
        
        <canvas id="testCanvas" width="600" height="200"></canvas>
        
        <div>
            <button class="btn" onclick="testECG()">اختبار محاكي ECG</button>
            <button class="btn" onclick="testFunctions()">اختبار الوظائف</button>
            <button class="btn" onclick="openMainPage()">فتح الصفحة الرئيسية</button>
        </div>
        
        <div id="results" style="margin-top: 20px; text-align: right;"></div>
    </div>

    <script>
        function testECG() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.fillStyle = '#001100';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw ECG-like waveform
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            let x = 0;
            let time = 0;
            
            function drawWave() {
                if (x >= canvas.width) {
                    x = 0;
                    ctx.fillStyle = '#001100';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    ctx.beginPath();
                }
                
                // Simple ECG simulation
                const heartRate = 75;
                const period = 60 / heartRate;
                const phase = (time % period) / period;
                
                let y = canvas.height / 2;
                
                // QRS complex simulation
                if (phase >= 0.15 && phase <= 0.25) {
                    const qrsPhase = (phase - 0.15) / 0.1;
                    if (qrsPhase < 0.5) {
                        y += 80 * Math.sin(Math.PI * qrsPhase / 0.5);
                    } else {
                        y -= 40 * Math.sin(Math.PI * (qrsPhase - 0.5) / 0.5);
                    }
                }
                
                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
                ctx.stroke();
                
                x += 2;
                time += 0.01;
                
                requestAnimationFrame(drawWave);
            }
            
            drawWave();
            document.getElementById('status').textContent = 'محاكي ECG يعمل بنجاح! ✅';
        }
        
        function testFunctions() {
            const results = document.getElementById('results');
            let testResults = '<h3>نتائج اختبار الوظائف:</h3><ul style="text-align: right;">';
            
            // Test basic JavaScript functions
            try {
                // Test Canvas API
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                testResults += '<li>✅ Canvas API متاح</li>';
            } catch (e) {
                testResults += '<li>❌ Canvas API غير متاح</li>';
            }
            
            // Test Math functions
            try {
                const sine = Math.sin(Math.PI / 2);
                testResults += '<li>✅ Math functions تعمل</li>';
            } catch (e) {
                testResults += '<li>❌ Math functions لا تعمل</li>';
            }
            
            // Test Date functions
            try {
                const now = new Date();
                testResults += '<li>✅ Date functions تعمل</li>';
            } catch (e) {
                testResults += '<li>❌ Date functions لا تعمل</li>';
            }
            
            // Test localStorage
            try {
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                testResults += '<li>✅ localStorage متاح</li>';
            } catch (e) {
                testResults += '<li>❌ localStorage غير متاح</li>';
            }
            
            testResults += '</ul>';
            results.innerHTML = testResults;
            document.getElementById('status').textContent = 'تم اختبار الوظائف بنجاح! 🔧';
        }
        
        function openMainPage() {
            try {
                window.open('تجربة تخطيط كهربية القلب (ECG).html', '_blank');
                document.getElementById('status').textContent = 'تم فتح الصفحة الرئيسية! 🚀';
            } catch (e) {
                document.getElementById('status').textContent = 'خطأ في فتح الصفحة: ' + e.message;
            }
        }
        
        // Auto-test on load
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('status').textContent = 'تم تحميل الصفحة بنجاح! جاهز للاختبار 🎯';
            }, 1000);
        });
    </script>
</body>
</html>
