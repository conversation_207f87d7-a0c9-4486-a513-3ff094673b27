<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مختبر ECG - نسخة مبسطة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
            text-align: center;
        }

        .ecg-monitor {
            background: #000;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .ecg-canvas {
            width: 100%;
            height: 200px;
            border-radius: 5px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .measurements {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .measurement-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .measurement-item:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .measurement-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .measurement-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .circuit-section {
            grid-column: 1 / -1;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .circuit-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .tab-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            color: #495057;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active,
        .tab-btn:hover {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }

        .circuit-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed #dee2e6;
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .measurements {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Animation for heartbeat */
        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .heartbeat {
            animation: heartbeat 1s infinite;
        }
    </style>
</head>
<body>
    <div class="status-indicator" id="status">
        <i class="fas fa-circle" style="color: #28a745;"></i> متصل
    </div>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-heartbeat heartbeat"></i> مختبر تخطيط كهربية القلب</h1>
            <p>نظام متقدم لقياس وتحليل الإشارات الكهروفسيولوجية</p>
        </div>

        <div class="main-content">
            <!-- ECG Monitor -->
            <div class="card">
                <h2><i class="fas fa-chart-line"></i> مراقب ECG</h2>
                <div class="ecg-monitor">
                    <canvas id="ecgCanvas" class="ecg-canvas" width="400" height="200"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="toggleSimulation()">
                        <i class="fas fa-play" id="playIcon"></i>
                        <span id="playText">بدء</span>
                    </button>
                    <button class="btn" onclick="resetSimulation()">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </button>
                    <button class="btn" onclick="saveECG()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>

            <!-- Measurements -->
            <div class="card">
                <h2><i class="fas fa-tachometer-alt"></i> القياسات المباشرة</h2>
                <div class="measurements">
                    <div class="measurement-item">
                        <span class="measurement-value" id="heartRate">75</span>
                        <div class="measurement-label">معدل القلب (BPM)</div>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-value" id="voltage">1.2</span>
                        <div class="measurement-label">الجهد (mV)</div>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-value" id="rrInterval">800</span>
                        <div class="measurement-label">فترة R-R (ms)</div>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-value" id="quality">95</span>
                        <div class="measurement-label">جودة الإشارة (%)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Circuit Diagrams -->
        <div class="circuit-section">
            <h2 style="text-align: center; margin-bottom: 30px;">
                <i class="fas fa-microchip"></i> الدوائر الإلكترونية
            </h2>
            <div class="circuit-tabs">
                <button class="tab-btn active" onclick="showCircuit('complete')">الدائرة الكاملة</button>
                <button class="tab-btn" onclick="showCircuit('amplifier')">مضخم الإشارة</button>
                <button class="tab-btn" onclick="showCircuit('filter')">المرشحات</button>
                <button class="tab-btn" onclick="showCircuit('reference')">دائرة المرجع</button>
            </div>
            <div class="circuit-display" id="circuitDisplay">
                <div style="text-align: center; color: #666;">
                    <i class="fas fa-microchip" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h3>انقر على التبويبات أعلاه لعرض الدوائر المختلفة</h3>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple ECG Simulator
        class SimpleECGSimulator {
            constructor() {
                this.canvas = document.getElementById('ecgCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.isRunning = false;
                this.time = 0;
                this.heartRate = 75;
                this.amplitude = 1.2;
                
                this.setupCanvas();
                this.updateMeasurements();
            }
            
            setupCanvas() {
                const rect = this.canvas.getBoundingClientRect();
                this.canvas.width = rect.width;
                this.canvas.height = rect.height;
            }
            
            start() {
                this.isRunning = true;
                this.animate();
            }
            
            stop() {
                this.isRunning = false;
            }
            
            reset() {
                this.time = 0;
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            }
            
            animate() {
                if (!this.isRunning) return;
                
                this.time += 0.02;
                this.drawECG();
                this.updateMeasurements();
                
                requestAnimationFrame(() => this.animate());
            }
            
            drawECG() {
                const ctx = this.ctx;
                const width = this.canvas.width;
                const height = this.canvas.height;
                
                // Clear canvas
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, width, height);
                
                // Draw grid
                ctx.strokeStyle = '#003300';
                ctx.lineWidth = 1;
                for (let x = 0; x < width; x += 20) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, height);
                    ctx.stroke();
                }
                for (let y = 0; y < height; y += 20) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(width, y);
                    ctx.stroke();
                }
                
                // Draw ECG waveform
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                const centerY = height / 2;
                const timeSpan = 3; // seconds
                
                for (let x = 0; x < width; x++) {
                    const t = (x / width) * timeSpan + this.time;
                    const amplitude = this.generateECGPoint(t);
                    const y = centerY - (amplitude * height * 0.3);
                    
                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                
                ctx.stroke();
            }
            
            generateECGPoint(t) {
                const cycleTime = t % (60 / this.heartRate);
                const normalizedTime = cycleTime / (60 / this.heartRate);
                
                let amplitude = 0;
                
                // P wave
                if (normalizedTime >= 0.0 && normalizedTime <= 0.15) {
                    const pTime = (normalizedTime - 0.075) / 0.075;
                    amplitude += 0.2 * Math.exp(-Math.pow(pTime * 3, 2));
                }
                
                // QRS complex
                if (normalizedTime >= 0.25 && normalizedTime <= 0.35) {
                    const qrsTime = (normalizedTime - 0.3) / 0.05;
                    amplitude += this.amplitude * Math.exp(-Math.pow(qrsTime * 5, 2));
                }
                
                // T wave
                if (normalizedTime >= 0.5 && normalizedTime <= 0.7) {
                    const tTime = (normalizedTime - 0.6) / 0.1;
                    amplitude += 0.3 * Math.exp(-Math.pow(tTime * 2, 2));
                }
                
                // Add some noise
                amplitude += (Math.random() - 0.5) * 0.05;
                
                return amplitude;
            }
            
            updateMeasurements() {
                // Simulate realistic variations
                const hrVariation = (Math.sin(this.time / 10) + Math.random() - 0.5) * 5;
                const currentHR = Math.max(60, Math.min(100, this.heartRate + hrVariation));
                
                document.getElementById('heartRate').textContent = Math.round(currentHR);
                document.getElementById('voltage').textContent = (this.amplitude + (Math.random() - 0.5) * 0.2).toFixed(1);
                document.getElementById('rrInterval').textContent = Math.round(60000 / currentHR);
                document.getElementById('quality').textContent = Math.round(90 + Math.random() * 10);
            }
        }
        
        // Global variables
        let simulator;
        let isRunning = false;
        
        // Control functions
        function toggleSimulation() {
            if (!simulator) {
                simulator = new SimpleECGSimulator();
            }
            
            if (isRunning) {
                simulator.stop();
                document.getElementById('playIcon').className = 'fas fa-play';
                document.getElementById('playText').textContent = 'بدء';
                document.getElementById('status').innerHTML = '<i class="fas fa-circle" style="color: #ffc107;"></i> متوقف';
            } else {
                simulator.start();
                document.getElementById('playIcon').className = 'fas fa-pause';
                document.getElementById('playText').textContent = 'إيقاف';
                document.getElementById('status').innerHTML = '<i class="fas fa-circle" style="color: #28a745;"></i> يعمل';
            }
            
            isRunning = !isRunning;
        }
        
        function resetSimulation() {
            if (simulator) {
                simulator.reset();
                simulator.time = 0;
            }
            document.getElementById('status').innerHTML = '<i class="fas fa-circle" style="color: #17a2b8;"></i> تم إعادة التعيين';
        }
        
        function saveECG() {
            if (simulator && simulator.canvas) {
                const link = document.createElement('a');
                link.download = `ecg-${new Date().toISOString().slice(0, 19)}.png`;
                link.href = simulator.canvas.toDataURL();
                link.click();
                document.getElementById('status').innerHTML = '<i class="fas fa-circle" style="color: #28a745;"></i> تم الحفظ';
            }
        }
        
        function showCircuit(type) {
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            const display = document.getElementById('circuitDisplay');
            const circuits = {
                complete: `
                    <div style="text-align: center;">
                        <h3>الدائرة الكاملة لتسجيل ECG</h3>
                        <div style="margin: 20px 0; font-family: monospace; background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <div>الأقطاب ← مضخم الأجهزة (AD623) ← مرشح عالي التمرير ← مرشح منخفض التمرير ← الخرج</div>
                            <div style="margin-top: 10px; color: #666;">
                                الكسب: 100 | نطاق التردد: 0.5 - 150 Hz | جهد المرجع: 2.5V
                            </div>
                        </div>
                    </div>
                `,
                amplifier: `
                    <div style="text-align: center;">
                        <h3>مضخم الأجهزة AD623</h3>
                        <div style="margin: 20px 0; font-family: monospace; background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <div>الكسب = 100 (Rg = 1kΩ)</div>
                            <div>CMRR > 80 dB</div>
                            <div>جهد الدخل: ±10V</div>
                            <div>تيار الدخل: 25 nA</div>
                        </div>
                    </div>
                `,
                filter: `
                    <div style="text-align: center;">
                        <h3>دوائر المرشحات</h3>
                        <div style="margin: 20px 0; font-family: monospace; background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <div><strong>مرشح عالي التمرير:</strong> fc = 0.5 Hz (R=330kΩ, C=1µF)</div>
                            <div style="margin: 10px 0;"><strong>مرشح منخفض التمرير:</strong> fc = 150 Hz (R=10kΩ, C=0.1µF)</div>
                            <div>نطاق التمرير: 0.5 - 150 Hz</div>
                        </div>
                    </div>
                `,
                reference: `
                    <div style="text-align: center;">
                        <h3>دائرة المرجع</h3>
                        <div style="margin: 20px 0; font-family: monospace; background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <div>مقسم الجهد: R1 = R2 = 10kΩ</div>
                            <div>المخزن المؤقت: MCP6002</div>
                            <div>جهد الخرج: Vref = Vcc/2 = 2.5V</div>
                            <div>الاستقرار: ±1%</div>
                        </div>
                    </div>
                `
            };
            
            display.innerHTML = circuits[type] || circuits.complete;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ECG Lab Simple - تم التحميل بنجاح');
            
            // Auto-start simulation after 2 seconds
            setTimeout(() => {
                toggleSimulation();
            }, 2000);
        });
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (simulator) {
                simulator.setupCanvas();
            }
        });
    </script>
</body>
</html>
