import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Home from './pages/Home';
import ECGExperiment from './pages/ECGExperiment';
import EMGExperiment from './pages/EMGExperiment';
import EEGExperiment from './pages/EEGExperiment';
import EOGExperiment from './pages/EOGExperiment';
import ENGExperiment from './pages/ENGExperiment';
import EGGExperiment from './pages/EGGExperiment';
import Footer from './components/Footer';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/ecg" element={<ECGExperiment />} />
            <Route path="/emg" element={<EMGExperiment />} />
            <Route path="/eeg" element={<EEGExperiment />} />
            <Route path="/eog" element={<EOGExperiment />} />
            <Route path="/eng" element={<ENGExperiment />} />
            <Route path="/egg" element={<EGGExperiment />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;