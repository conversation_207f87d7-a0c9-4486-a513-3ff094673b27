(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const i of l)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(l){const i={};return l.integrity&&(i.integrity=l.integrity),l.referrerPolicy&&(i.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?i.credentials="include":l.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(l){if(l.ep)return;l.ep=!0;const i=n(l);fetch(l.href,i)}})();var Ma={exports:{}},kl={},Oa={exports:{}},M={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ar=Symbol.for("react.element"),fd=Symbol.for("react.portal"),pd=Symbol.for("react.fragment"),md=Symbol.for("react.strict_mode"),hd=Symbol.for("react.profiler"),xd=Symbol.for("react.provider"),yd=Symbol.for("react.context"),gd=Symbol.for("react.forward_ref"),vd=Symbol.for("react.suspense"),wd=Symbol.for("react.memo"),kd=Symbol.for("react.lazy"),xs=Symbol.iterator;function Nd(e){return e===null||typeof e!="object"?null:(e=xs&&e[xs]||e["@@iterator"],typeof e=="function"?e:null)}var Ia={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Da=Object.assign,Fa={};function hn(e,t,n){this.props=e,this.context=t,this.refs=Fa,this.updater=n||Ia}hn.prototype.isReactComponent={};hn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};hn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Aa(){}Aa.prototype=hn.prototype;function po(e,t,n){this.props=e,this.context=t,this.refs=Fa,this.updater=n||Ia}var mo=po.prototype=new Aa;mo.constructor=po;Da(mo,hn.prototype);mo.isPureReactComponent=!0;var ys=Array.isArray,$a=Object.prototype.hasOwnProperty,ho={current:null},Ua={key:!0,ref:!0,__self:!0,__source:!0};function Wa(e,t,n){var r,l={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)$a.call(t,r)&&!Ua.hasOwnProperty(r)&&(l[r]=t[r]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)l[r]===void 0&&(l[r]=a[r]);return{$$typeof:ar,type:e,key:i,ref:o,props:l,_owner:ho.current}}function jd(e,t){return{$$typeof:ar,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function xo(e){return typeof e=="object"&&e!==null&&e.$$typeof===ar}function Sd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var gs=/\/+/g;function Hl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Sd(""+e.key):t.toString(36)}function Dr(e,t,n,r,l){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ar:case fd:o=!0}}if(o)return o=e,l=l(o),e=r===""?"."+Hl(o,0):r,ys(l)?(n="",e!=null&&(n=e.replace(gs,"$&/")+"/"),Dr(l,t,n,"",function(c){return c})):l!=null&&(xo(l)&&(l=jd(l,n+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(gs,"$&/")+"/")+e)),t.push(l)),1;if(o=0,r=r===""?".":r+":",ys(e))for(var a=0;a<e.length;a++){i=e[a];var u=r+Hl(i,a);o+=Dr(i,t,n,u,l)}else if(u=Nd(e),typeof u=="function")for(e=u.call(e),a=0;!(i=e.next()).done;)i=i.value,u=r+Hl(i,a++),o+=Dr(i,t,n,u,l);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function gr(e,t,n){if(e==null)return e;var r=[],l=0;return Dr(e,r,"","",function(i){return t.call(n,i,l++)}),r}function Ed(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ce={current:null},Fr={transition:null},Cd={ReactCurrentDispatcher:ce,ReactCurrentBatchConfig:Fr,ReactCurrentOwner:ho};function Va(){throw Error("act(...) is not supported in production builds of React.")}M.Children={map:gr,forEach:function(e,t,n){gr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return gr(e,function(){t++}),t},toArray:function(e){return gr(e,function(t){return t})||[]},only:function(e){if(!xo(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};M.Component=hn;M.Fragment=pd;M.Profiler=hd;M.PureComponent=po;M.StrictMode=md;M.Suspense=vd;M.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cd;M.act=Va;M.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Da({},e.props),l=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=ho.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)$a.call(t,u)&&!Ua.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:ar,type:e.type,key:l,ref:i,props:r,_owner:o}};M.createContext=function(e){return e={$$typeof:yd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:xd,_context:e},e.Consumer=e};M.createElement=Wa;M.createFactory=function(e){var t=Wa.bind(null,e);return t.type=e,t};M.createRef=function(){return{current:null}};M.forwardRef=function(e){return{$$typeof:gd,render:e}};M.isValidElement=xo;M.lazy=function(e){return{$$typeof:kd,_payload:{_status:-1,_result:e},_init:Ed}};M.memo=function(e,t){return{$$typeof:wd,type:e,compare:t===void 0?null:t}};M.startTransition=function(e){var t=Fr.transition;Fr.transition={};try{e()}finally{Fr.transition=t}};M.unstable_act=Va;M.useCallback=function(e,t){return ce.current.useCallback(e,t)};M.useContext=function(e){return ce.current.useContext(e)};M.useDebugValue=function(){};M.useDeferredValue=function(e){return ce.current.useDeferredValue(e)};M.useEffect=function(e,t){return ce.current.useEffect(e,t)};M.useId=function(){return ce.current.useId()};M.useImperativeHandle=function(e,t,n){return ce.current.useImperativeHandle(e,t,n)};M.useInsertionEffect=function(e,t){return ce.current.useInsertionEffect(e,t)};M.useLayoutEffect=function(e,t){return ce.current.useLayoutEffect(e,t)};M.useMemo=function(e,t){return ce.current.useMemo(e,t)};M.useReducer=function(e,t,n){return ce.current.useReducer(e,t,n)};M.useRef=function(e){return ce.current.useRef(e)};M.useState=function(e){return ce.current.useState(e)};M.useSyncExternalStore=function(e,t,n){return ce.current.useSyncExternalStore(e,t,n)};M.useTransition=function(){return ce.current.useTransition()};M.version="18.3.1";Oa.exports=M;var y=Oa.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rd=y,Pd=Symbol.for("react.element"),Ld=Symbol.for("react.fragment"),_d=Object.prototype.hasOwnProperty,zd=Rd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Td={key:!0,ref:!0,__self:!0,__source:!0};function Ha(e,t,n){var r,l={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)_d.call(t,r)&&!Td.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:Pd,type:e,key:i,ref:o,props:l,_owner:zd.current}}kl.Fragment=Ld;kl.jsx=Ha;kl.jsxs=Ha;Ma.exports=kl;var s=Ma.exports,Ba={exports:{}},Ne={},Ga={exports:{}},Qa={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(C,z){var T=C.length;C.push(z);e:for(;0<T;){var Q=T-1>>>1,J=C[Q];if(0<l(J,z))C[Q]=z,C[T]=J,T=Q;else break e}}function n(C){return C.length===0?null:C[0]}function r(C){if(C.length===0)return null;var z=C[0],T=C.pop();if(T!==z){C[0]=T;e:for(var Q=0,J=C.length,xr=J>>>1;Q<xr;){var Ct=2*(Q+1)-1,Vl=C[Ct],Rt=Ct+1,yr=C[Rt];if(0>l(Vl,T))Rt<J&&0>l(yr,Vl)?(C[Q]=yr,C[Rt]=T,Q=Rt):(C[Q]=Vl,C[Ct]=T,Q=Ct);else if(Rt<J&&0>l(yr,T))C[Q]=yr,C[Rt]=T,Q=Rt;else break e}}return z}function l(C,z){var T=C.sortIndex-z.sortIndex;return T!==0?T:C.id-z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var u=[],c=[],h=1,x=null,m=3,w=!1,g=!1,v=!1,E=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(C){for(var z=n(c);z!==null;){if(z.callback===null)r(c);else if(z.startTime<=C)r(c),z.sortIndex=z.expirationTime,t(u,z);else break;z=n(c)}}function k(C){if(v=!1,p(C),!g)if(n(u)!==null)g=!0,Ul(j);else{var z=n(c);z!==null&&Wl(k,z.startTime-C)}}function j(C,z){g=!1,v&&(v=!1,f(L),L=-1),w=!0;var T=m;try{for(p(z),x=n(u);x!==null&&(!(x.expirationTime>z)||C&&!fe());){var Q=x.callback;if(typeof Q=="function"){x.callback=null,m=x.priorityLevel;var J=Q(x.expirationTime<=z);z=e.unstable_now(),typeof J=="function"?x.callback=J:x===n(u)&&r(u),p(z)}else r(u);x=n(u)}if(x!==null)var xr=!0;else{var Ct=n(c);Ct!==null&&Wl(k,Ct.startTime-z),xr=!1}return xr}finally{x=null,m=T,w=!1}}var P=!1,R=null,L=-1,O=5,_=-1;function fe(){return!(e.unstable_now()-_<O)}function wn(){if(R!==null){var C=e.unstable_now();_=C;var z=!0;try{z=R(!0,C)}finally{z?kn():(P=!1,R=null)}}else P=!1}var kn;if(typeof d=="function")kn=function(){d(wn)};else if(typeof MessageChannel<"u"){var hs=new MessageChannel,dd=hs.port2;hs.port1.onmessage=wn,kn=function(){dd.postMessage(null)}}else kn=function(){E(wn,0)};function Ul(C){R=C,P||(P=!0,kn())}function Wl(C,z){L=E(function(){C(e.unstable_now())},z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(C){C.callback=null},e.unstable_continueExecution=function(){g||w||(g=!0,Ul(j))},e.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<C?Math.floor(1e3/C):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(C){switch(m){case 1:case 2:case 3:var z=3;break;default:z=m}var T=m;m=z;try{return C()}finally{m=T}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(C,z){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var T=m;m=C;try{return z()}finally{m=T}},e.unstable_scheduleCallback=function(C,z,T){var Q=e.unstable_now();switch(typeof T=="object"&&T!==null?(T=T.delay,T=typeof T=="number"&&0<T?Q+T:Q):T=Q,C){case 1:var J=-1;break;case 2:J=250;break;case 5:J=**********;break;case 4:J=1e4;break;default:J=5e3}return J=T+J,C={id:h++,callback:z,priorityLevel:C,startTime:T,expirationTime:J,sortIndex:-1},T>Q?(C.sortIndex=T,t(c,C),n(u)===null&&C===n(c)&&(v?(f(L),L=-1):v=!0,Wl(k,T-Q))):(C.sortIndex=J,t(u,C),g||w||(g=!0,Ul(j))),C},e.unstable_shouldYield=fe,e.unstable_wrapCallback=function(C){var z=m;return function(){var T=m;m=z;try{return C.apply(this,arguments)}finally{m=T}}}})(Qa);Ga.exports=Qa;var Md=Ga.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Od=y,ke=Md;function N(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ka=new Set,Bn={};function Ut(e,t){an(e,t),an(e+"Capture",t)}function an(e,t){for(Bn[e]=t,e=0;e<t.length;e++)Ka.add(t[e])}var Je=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),yi=Object.prototype.hasOwnProperty,Id=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,vs={},ws={};function Dd(e){return yi.call(ws,e)?!0:yi.call(vs,e)?!1:Id.test(e)?ws[e]=!0:(vs[e]=!0,!1)}function Fd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ad(e,t,n,r){if(t===null||typeof t>"u"||Fd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function de(e,t,n,r,l,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var ne={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ne[e]=new de(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ne[t]=new de(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ne[e]=new de(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ne[e]=new de(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ne[e]=new de(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ne[e]=new de(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ne[e]=new de(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ne[e]=new de(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ne[e]=new de(e,5,!1,e.toLowerCase(),null,!1,!1)});var yo=/[\-:]([a-z])/g;function go(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(yo,go);ne[t]=new de(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(yo,go);ne[t]=new de(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(yo,go);ne[t]=new de(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ne[e]=new de(e,1,!1,e.toLowerCase(),null,!1,!1)});ne.xlinkHref=new de("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ne[e]=new de(e,1,!1,e.toLowerCase(),null,!0,!0)});function vo(e,t,n,r){var l=ne.hasOwnProperty(t)?ne[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ad(t,n,l,r)&&(n=null),r||l===null?Dd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var nt=Od.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,vr=Symbol.for("react.element"),Ht=Symbol.for("react.portal"),Bt=Symbol.for("react.fragment"),wo=Symbol.for("react.strict_mode"),gi=Symbol.for("react.profiler"),Ya=Symbol.for("react.provider"),Xa=Symbol.for("react.context"),ko=Symbol.for("react.forward_ref"),vi=Symbol.for("react.suspense"),wi=Symbol.for("react.suspense_list"),No=Symbol.for("react.memo"),ot=Symbol.for("react.lazy"),Za=Symbol.for("react.offscreen"),ks=Symbol.iterator;function Nn(e){return e===null||typeof e!="object"?null:(e=ks&&e[ks]||e["@@iterator"],typeof e=="function"?e:null)}var B=Object.assign,Bl;function zn(e){if(Bl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Bl=t&&t[1]||""}return`
`+Bl+e}var Gl=!1;function Ql(e,t){if(!e||Gl)return"";Gl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),i=r.stack.split(`
`),o=l.length-1,a=i.length-1;1<=o&&0<=a&&l[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(l[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||l[o]!==i[a]){var u=`
`+l[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=a);break}}}finally{Gl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?zn(e):""}function $d(e){switch(e.tag){case 5:return zn(e.type);case 16:return zn("Lazy");case 13:return zn("Suspense");case 19:return zn("SuspenseList");case 0:case 2:case 15:return e=Ql(e.type,!1),e;case 11:return e=Ql(e.type.render,!1),e;case 1:return e=Ql(e.type,!0),e;default:return""}}function ki(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Bt:return"Fragment";case Ht:return"Portal";case gi:return"Profiler";case wo:return"StrictMode";case vi:return"Suspense";case wi:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Xa:return(e.displayName||"Context")+".Consumer";case Ya:return(e._context.displayName||"Context")+".Provider";case ko:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case No:return t=e.displayName||null,t!==null?t:ki(e.type)||"Memo";case ot:t=e._payload,e=e._init;try{return ki(e(t))}catch{}}return null}function Ud(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ki(t);case 8:return t===wo?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function wt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ja(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Wd(e){var t=Ja(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function wr(e){e._valueTracker||(e._valueTracker=Wd(e))}function qa(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ja(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Zr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ni(e,t){var n=t.checked;return B({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ns(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=wt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ba(e,t){t=t.checked,t!=null&&vo(e,"checked",t,!1)}function ji(e,t){ba(e,t);var n=wt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Si(e,t.type,n):t.hasOwnProperty("defaultValue")&&Si(e,t.type,wt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function js(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Si(e,t,n){(t!=="number"||Zr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Tn=Array.isArray;function tn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+wt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Ei(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(N(91));return B({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ss(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(N(92));if(Tn(n)){if(1<n.length)throw Error(N(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:wt(n)}}function eu(e,t){var n=wt(t.value),r=wt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Es(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function tu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ci(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?tu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var kr,nu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(kr=kr||document.createElement("div"),kr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=kr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Gn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var In={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Vd=["Webkit","ms","Moz","O"];Object.keys(In).forEach(function(e){Vd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),In[t]=In[e]})});function ru(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||In.hasOwnProperty(e)&&In[e]?(""+t).trim():t+"px"}function lu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=ru(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Hd=B({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ri(e,t){if(t){if(Hd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(N(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(N(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(N(61))}if(t.style!=null&&typeof t.style!="object")throw Error(N(62))}}function Pi(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Li=null;function jo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _i=null,nn=null,rn=null;function Cs(e){if(e=dr(e)){if(typeof _i!="function")throw Error(N(280));var t=e.stateNode;t&&(t=Cl(t),_i(e.stateNode,e.type,t))}}function iu(e){nn?rn?rn.push(e):rn=[e]:nn=e}function ou(){if(nn){var e=nn,t=rn;if(rn=nn=null,Cs(e),t)for(e=0;e<t.length;e++)Cs(t[e])}}function su(e,t){return e(t)}function au(){}var Kl=!1;function uu(e,t,n){if(Kl)return e(t,n);Kl=!0;try{return su(e,t,n)}finally{Kl=!1,(nn!==null||rn!==null)&&(au(),ou())}}function Qn(e,t){var n=e.stateNode;if(n===null)return null;var r=Cl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(N(231,t,typeof n));return n}var zi=!1;if(Je)try{var jn={};Object.defineProperty(jn,"passive",{get:function(){zi=!0}}),window.addEventListener("test",jn,jn),window.removeEventListener("test",jn,jn)}catch{zi=!1}function Bd(e,t,n,r,l,i,o,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(h){this.onError(h)}}var Dn=!1,Jr=null,qr=!1,Ti=null,Gd={onError:function(e){Dn=!0,Jr=e}};function Qd(e,t,n,r,l,i,o,a,u){Dn=!1,Jr=null,Bd.apply(Gd,arguments)}function Kd(e,t,n,r,l,i,o,a,u){if(Qd.apply(this,arguments),Dn){if(Dn){var c=Jr;Dn=!1,Jr=null}else throw Error(N(198));qr||(qr=!0,Ti=c)}}function Wt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function cu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Rs(e){if(Wt(e)!==e)throw Error(N(188))}function Yd(e){var t=e.alternate;if(!t){if(t=Wt(e),t===null)throw Error(N(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var i=l.alternate;if(i===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===n)return Rs(l),e;if(i===r)return Rs(l),t;i=i.sibling}throw Error(N(188))}if(n.return!==r.return)n=l,r=i;else{for(var o=!1,a=l.child;a;){if(a===n){o=!0,n=l,r=i;break}if(a===r){o=!0,r=l,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=l;break}if(a===r){o=!0,r=i,n=l;break}a=a.sibling}if(!o)throw Error(N(189))}}if(n.alternate!==r)throw Error(N(190))}if(n.tag!==3)throw Error(N(188));return n.stateNode.current===n?e:t}function du(e){return e=Yd(e),e!==null?fu(e):null}function fu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=fu(e);if(t!==null)return t;e=e.sibling}return null}var pu=ke.unstable_scheduleCallback,Ps=ke.unstable_cancelCallback,Xd=ke.unstable_shouldYield,Zd=ke.unstable_requestPaint,K=ke.unstable_now,Jd=ke.unstable_getCurrentPriorityLevel,So=ke.unstable_ImmediatePriority,mu=ke.unstable_UserBlockingPriority,br=ke.unstable_NormalPriority,qd=ke.unstable_LowPriority,hu=ke.unstable_IdlePriority,Nl=null,Ue=null;function bd(e){if(Ue&&typeof Ue.onCommitFiberRoot=="function")try{Ue.onCommitFiberRoot(Nl,e,void 0,(e.current.flags&128)===128)}catch{}}var Oe=Math.clz32?Math.clz32:nf,ef=Math.log,tf=Math.LN2;function nf(e){return e>>>=0,e===0?32:31-(ef(e)/tf|0)|0}var Nr=64,jr=4194304;function Mn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function el(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~l;a!==0?r=Mn(a):(i&=o,i!==0&&(r=Mn(i)))}else o=n&~l,o!==0?r=Mn(o):i!==0&&(r=Mn(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,i=t&-t,l>=i||l===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Oe(t),l=1<<n,r|=e[n],t&=~l;return r}function rf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function lf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Oe(i),a=1<<o,u=l[o];u===-1?(!(a&n)||a&r)&&(l[o]=rf(a,t)):u<=t&&(e.expiredLanes|=a),i&=~a}}function Mi(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function xu(){var e=Nr;return Nr<<=1,!(Nr&4194240)&&(Nr=64),e}function Yl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ur(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Oe(t),e[t]=n}function of(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Oe(n),i=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~i}}function Eo(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Oe(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var D=0;function yu(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var gu,Co,vu,wu,ku,Oi=!1,Sr=[],ft=null,pt=null,mt=null,Kn=new Map,Yn=new Map,at=[],sf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ls(e,t){switch(e){case"focusin":case"focusout":ft=null;break;case"dragenter":case"dragleave":pt=null;break;case"mouseover":case"mouseout":mt=null;break;case"pointerover":case"pointerout":Kn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Yn.delete(t.pointerId)}}function Sn(e,t,n,r,l,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[l]},t!==null&&(t=dr(t),t!==null&&Co(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function af(e,t,n,r,l){switch(t){case"focusin":return ft=Sn(ft,e,t,n,r,l),!0;case"dragenter":return pt=Sn(pt,e,t,n,r,l),!0;case"mouseover":return mt=Sn(mt,e,t,n,r,l),!0;case"pointerover":var i=l.pointerId;return Kn.set(i,Sn(Kn.get(i)||null,e,t,n,r,l)),!0;case"gotpointercapture":return i=l.pointerId,Yn.set(i,Sn(Yn.get(i)||null,e,t,n,r,l)),!0}return!1}function Nu(e){var t=_t(e.target);if(t!==null){var n=Wt(t);if(n!==null){if(t=n.tag,t===13){if(t=cu(n),t!==null){e.blockedOn=t,ku(e.priority,function(){vu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ar(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ii(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Li=r,n.target.dispatchEvent(r),Li=null}else return t=dr(n),t!==null&&Co(t),e.blockedOn=n,!1;t.shift()}return!0}function _s(e,t,n){Ar(e)&&n.delete(t)}function uf(){Oi=!1,ft!==null&&Ar(ft)&&(ft=null),pt!==null&&Ar(pt)&&(pt=null),mt!==null&&Ar(mt)&&(mt=null),Kn.forEach(_s),Yn.forEach(_s)}function En(e,t){e.blockedOn===t&&(e.blockedOn=null,Oi||(Oi=!0,ke.unstable_scheduleCallback(ke.unstable_NormalPriority,uf)))}function Xn(e){function t(l){return En(l,e)}if(0<Sr.length){En(Sr[0],e);for(var n=1;n<Sr.length;n++){var r=Sr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(ft!==null&&En(ft,e),pt!==null&&En(pt,e),mt!==null&&En(mt,e),Kn.forEach(t),Yn.forEach(t),n=0;n<at.length;n++)r=at[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<at.length&&(n=at[0],n.blockedOn===null);)Nu(n),n.blockedOn===null&&at.shift()}var ln=nt.ReactCurrentBatchConfig,tl=!0;function cf(e,t,n,r){var l=D,i=ln.transition;ln.transition=null;try{D=1,Ro(e,t,n,r)}finally{D=l,ln.transition=i}}function df(e,t,n,r){var l=D,i=ln.transition;ln.transition=null;try{D=4,Ro(e,t,n,r)}finally{D=l,ln.transition=i}}function Ro(e,t,n,r){if(tl){var l=Ii(e,t,n,r);if(l===null)li(e,t,r,nl,n),Ls(e,r);else if(af(l,e,t,n,r))r.stopPropagation();else if(Ls(e,r),t&4&&-1<sf.indexOf(e)){for(;l!==null;){var i=dr(l);if(i!==null&&gu(i),i=Ii(e,t,n,r),i===null&&li(e,t,r,nl,n),i===l)break;l=i}l!==null&&r.stopPropagation()}else li(e,t,r,null,n)}}var nl=null;function Ii(e,t,n,r){if(nl=null,e=jo(r),e=_t(e),e!==null)if(t=Wt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=cu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return nl=e,null}function ju(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Jd()){case So:return 1;case mu:return 4;case br:case qd:return 16;case hu:return 536870912;default:return 16}default:return 16}}var ct=null,Po=null,$r=null;function Su(){if($r)return $r;var e,t=Po,n=t.length,r,l="value"in ct?ct.value:ct.textContent,i=l.length;for(e=0;e<n&&t[e]===l[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===l[i-r];r++);return $r=l.slice(e,1<r?1-r:void 0)}function Ur(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Er(){return!0}function zs(){return!1}function je(e){function t(n,r,l,i,o){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Er:zs,this.isPropagationStopped=zs,this}return B(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Er)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Er)},persist:function(){},isPersistent:Er}),t}var xn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Lo=je(xn),cr=B({},xn,{view:0,detail:0}),ff=je(cr),Xl,Zl,Cn,jl=B({},cr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_o,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Cn&&(Cn&&e.type==="mousemove"?(Xl=e.screenX-Cn.screenX,Zl=e.screenY-Cn.screenY):Zl=Xl=0,Cn=e),Xl)},movementY:function(e){return"movementY"in e?e.movementY:Zl}}),Ts=je(jl),pf=B({},jl,{dataTransfer:0}),mf=je(pf),hf=B({},cr,{relatedTarget:0}),Jl=je(hf),xf=B({},xn,{animationName:0,elapsedTime:0,pseudoElement:0}),yf=je(xf),gf=B({},xn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vf=je(gf),wf=B({},xn,{data:0}),Ms=je(wf),kf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Nf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},jf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=jf[e])?!!t[e]:!1}function _o(){return Sf}var Ef=B({},cr,{key:function(e){if(e.key){var t=kf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ur(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Nf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_o,charCode:function(e){return e.type==="keypress"?Ur(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ur(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Cf=je(Ef),Rf=B({},jl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Os=je(Rf),Pf=B({},cr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_o}),Lf=je(Pf),_f=B({},xn,{propertyName:0,elapsedTime:0,pseudoElement:0}),zf=je(_f),Tf=B({},jl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mf=je(Tf),Of=[9,13,27,32],zo=Je&&"CompositionEvent"in window,Fn=null;Je&&"documentMode"in document&&(Fn=document.documentMode);var If=Je&&"TextEvent"in window&&!Fn,Eu=Je&&(!zo||Fn&&8<Fn&&11>=Fn),Is=" ",Ds=!1;function Cu(e,t){switch(e){case"keyup":return Of.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ru(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gt=!1;function Df(e,t){switch(e){case"compositionend":return Ru(t);case"keypress":return t.which!==32?null:(Ds=!0,Is);case"textInput":return e=t.data,e===Is&&Ds?null:e;default:return null}}function Ff(e,t){if(Gt)return e==="compositionend"||!zo&&Cu(e,t)?(e=Su(),$r=Po=ct=null,Gt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Eu&&t.locale!=="ko"?null:t.data;default:return null}}var Af={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Fs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Af[e.type]:t==="textarea"}function Pu(e,t,n,r){iu(r),t=rl(t,"onChange"),0<t.length&&(n=new Lo("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var An=null,Zn=null;function $f(e){$u(e,0)}function Sl(e){var t=Yt(e);if(qa(t))return e}function Uf(e,t){if(e==="change")return t}var Lu=!1;if(Je){var ql;if(Je){var bl="oninput"in document;if(!bl){var As=document.createElement("div");As.setAttribute("oninput","return;"),bl=typeof As.oninput=="function"}ql=bl}else ql=!1;Lu=ql&&(!document.documentMode||9<document.documentMode)}function $s(){An&&(An.detachEvent("onpropertychange",_u),Zn=An=null)}function _u(e){if(e.propertyName==="value"&&Sl(Zn)){var t=[];Pu(t,Zn,e,jo(e)),uu($f,t)}}function Wf(e,t,n){e==="focusin"?($s(),An=t,Zn=n,An.attachEvent("onpropertychange",_u)):e==="focusout"&&$s()}function Vf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Sl(Zn)}function Hf(e,t){if(e==="click")return Sl(t)}function Bf(e,t){if(e==="input"||e==="change")return Sl(t)}function Gf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var De=typeof Object.is=="function"?Object.is:Gf;function Jn(e,t){if(De(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!yi.call(t,l)||!De(e[l],t[l]))return!1}return!0}function Us(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ws(e,t){var n=Us(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Us(n)}}function zu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?zu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Tu(){for(var e=window,t=Zr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Zr(e.document)}return t}function To(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Qf(e){var t=Tu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&zu(n.ownerDocument.documentElement,n)){if(r!==null&&To(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,i=Math.min(r.start,l);r=r.end===void 0?i:Math.min(r.end,l),!e.extend&&i>r&&(l=r,r=i,i=l),l=Ws(n,i);var o=Ws(n,r);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Kf=Je&&"documentMode"in document&&11>=document.documentMode,Qt=null,Di=null,$n=null,Fi=!1;function Vs(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Fi||Qt==null||Qt!==Zr(r)||(r=Qt,"selectionStart"in r&&To(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),$n&&Jn($n,r)||($n=r,r=rl(Di,"onSelect"),0<r.length&&(t=new Lo("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Qt)))}function Cr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Kt={animationend:Cr("Animation","AnimationEnd"),animationiteration:Cr("Animation","AnimationIteration"),animationstart:Cr("Animation","AnimationStart"),transitionend:Cr("Transition","TransitionEnd")},ei={},Mu={};Je&&(Mu=document.createElement("div").style,"AnimationEvent"in window||(delete Kt.animationend.animation,delete Kt.animationiteration.animation,delete Kt.animationstart.animation),"TransitionEvent"in window||delete Kt.transitionend.transition);function El(e){if(ei[e])return ei[e];if(!Kt[e])return e;var t=Kt[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Mu)return ei[e]=t[n];return e}var Ou=El("animationend"),Iu=El("animationiteration"),Du=El("animationstart"),Fu=El("transitionend"),Au=new Map,Hs="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Nt(e,t){Au.set(e,t),Ut(t,[e])}for(var ti=0;ti<Hs.length;ti++){var ni=Hs[ti],Yf=ni.toLowerCase(),Xf=ni[0].toUpperCase()+ni.slice(1);Nt(Yf,"on"+Xf)}Nt(Ou,"onAnimationEnd");Nt(Iu,"onAnimationIteration");Nt(Du,"onAnimationStart");Nt("dblclick","onDoubleClick");Nt("focusin","onFocus");Nt("focusout","onBlur");Nt(Fu,"onTransitionEnd");an("onMouseEnter",["mouseout","mouseover"]);an("onMouseLeave",["mouseout","mouseover"]);an("onPointerEnter",["pointerout","pointerover"]);an("onPointerLeave",["pointerout","pointerover"]);Ut("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ut("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ut("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ut("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ut("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ut("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var On="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Zf=new Set("cancel close invalid load scroll toggle".split(" ").concat(On));function Bs(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Kd(r,t,void 0,e),e.currentTarget=null}function $u(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==i&&l.isPropagationStopped())break e;Bs(l,a,c),i=u}else for(o=0;o<r.length;o++){if(a=r[o],u=a.instance,c=a.currentTarget,a=a.listener,u!==i&&l.isPropagationStopped())break e;Bs(l,a,c),i=u}}}if(qr)throw e=Ti,qr=!1,Ti=null,e}function A(e,t){var n=t[Vi];n===void 0&&(n=t[Vi]=new Set);var r=e+"__bubble";n.has(r)||(Uu(t,e,2,!1),n.add(r))}function ri(e,t,n){var r=0;t&&(r|=4),Uu(n,e,r,t)}var Rr="_reactListening"+Math.random().toString(36).slice(2);function qn(e){if(!e[Rr]){e[Rr]=!0,Ka.forEach(function(n){n!=="selectionchange"&&(Zf.has(n)||ri(n,!1,e),ri(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Rr]||(t[Rr]=!0,ri("selectionchange",!1,t))}}function Uu(e,t,n,r){switch(ju(t)){case 1:var l=cf;break;case 4:l=df;break;default:l=Ro}n=l.bind(null,t,n,e),l=void 0,!zi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function li(e,t,n,r,l){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===l||u.nodeType===8&&u.parentNode===l))return;o=o.return}for(;a!==null;){if(o=_t(a),o===null)return;if(u=o.tag,u===5||u===6){r=i=o;continue e}a=a.parentNode}}r=r.return}uu(function(){var c=i,h=jo(n),x=[];e:{var m=Au.get(e);if(m!==void 0){var w=Lo,g=e;switch(e){case"keypress":if(Ur(n)===0)break e;case"keydown":case"keyup":w=Cf;break;case"focusin":g="focus",w=Jl;break;case"focusout":g="blur",w=Jl;break;case"beforeblur":case"afterblur":w=Jl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Ts;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=mf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Lf;break;case Ou:case Iu:case Du:w=yf;break;case Fu:w=zf;break;case"scroll":w=ff;break;case"wheel":w=Mf;break;case"copy":case"cut":case"paste":w=vf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Os}var v=(t&4)!==0,E=!v&&e==="scroll",f=v?m!==null?m+"Capture":null:m;v=[];for(var d=c,p;d!==null;){p=d;var k=p.stateNode;if(p.tag===5&&k!==null&&(p=k,f!==null&&(k=Qn(d,f),k!=null&&v.push(bn(d,k,p)))),E)break;d=d.return}0<v.length&&(m=new w(m,g,null,n,h),x.push({event:m,listeners:v}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",m&&n!==Li&&(g=n.relatedTarget||n.fromElement)&&(_t(g)||g[qe]))break e;if((w||m)&&(m=h.window===h?h:(m=h.ownerDocument)?m.defaultView||m.parentWindow:window,w?(g=n.relatedTarget||n.toElement,w=c,g=g?_t(g):null,g!==null&&(E=Wt(g),g!==E||g.tag!==5&&g.tag!==6)&&(g=null)):(w=null,g=c),w!==g)){if(v=Ts,k="onMouseLeave",f="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(v=Os,k="onPointerLeave",f="onPointerEnter",d="pointer"),E=w==null?m:Yt(w),p=g==null?m:Yt(g),m=new v(k,d+"leave",w,n,h),m.target=E,m.relatedTarget=p,k=null,_t(h)===c&&(v=new v(f,d+"enter",g,n,h),v.target=p,v.relatedTarget=E,k=v),E=k,w&&g)t:{for(v=w,f=g,d=0,p=v;p;p=Vt(p))d++;for(p=0,k=f;k;k=Vt(k))p++;for(;0<d-p;)v=Vt(v),d--;for(;0<p-d;)f=Vt(f),p--;for(;d--;){if(v===f||f!==null&&v===f.alternate)break t;v=Vt(v),f=Vt(f)}v=null}else v=null;w!==null&&Gs(x,m,w,v,!1),g!==null&&E!==null&&Gs(x,E,g,v,!0)}}e:{if(m=c?Yt(c):window,w=m.nodeName&&m.nodeName.toLowerCase(),w==="select"||w==="input"&&m.type==="file")var j=Uf;else if(Fs(m))if(Lu)j=Bf;else{j=Vf;var P=Wf}else(w=m.nodeName)&&w.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(j=Hf);if(j&&(j=j(e,c))){Pu(x,j,n,h);break e}P&&P(e,m,c),e==="focusout"&&(P=m._wrapperState)&&P.controlled&&m.type==="number"&&Si(m,"number",m.value)}switch(P=c?Yt(c):window,e){case"focusin":(Fs(P)||P.contentEditable==="true")&&(Qt=P,Di=c,$n=null);break;case"focusout":$n=Di=Qt=null;break;case"mousedown":Fi=!0;break;case"contextmenu":case"mouseup":case"dragend":Fi=!1,Vs(x,n,h);break;case"selectionchange":if(Kf)break;case"keydown":case"keyup":Vs(x,n,h)}var R;if(zo)e:{switch(e){case"compositionstart":var L="onCompositionStart";break e;case"compositionend":L="onCompositionEnd";break e;case"compositionupdate":L="onCompositionUpdate";break e}L=void 0}else Gt?Cu(e,n)&&(L="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(L="onCompositionStart");L&&(Eu&&n.locale!=="ko"&&(Gt||L!=="onCompositionStart"?L==="onCompositionEnd"&&Gt&&(R=Su()):(ct=h,Po="value"in ct?ct.value:ct.textContent,Gt=!0)),P=rl(c,L),0<P.length&&(L=new Ms(L,e,null,n,h),x.push({event:L,listeners:P}),R?L.data=R:(R=Ru(n),R!==null&&(L.data=R)))),(R=If?Df(e,n):Ff(e,n))&&(c=rl(c,"onBeforeInput"),0<c.length&&(h=new Ms("onBeforeInput","beforeinput",null,n,h),x.push({event:h,listeners:c}),h.data=R))}$u(x,t)})}function bn(e,t,n){return{instance:e,listener:t,currentTarget:n}}function rl(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,i=l.stateNode;l.tag===5&&i!==null&&(l=i,i=Qn(e,n),i!=null&&r.unshift(bn(e,i,l)),i=Qn(e,t),i!=null&&r.push(bn(e,i,l))),e=e.return}return r}function Vt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Gs(e,t,n,r,l){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,l?(u=Qn(n,i),u!=null&&o.unshift(bn(n,u,a))):l||(u=Qn(n,i),u!=null&&o.push(bn(n,u,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Jf=/\r\n?/g,qf=/\u0000|\uFFFD/g;function Qs(e){return(typeof e=="string"?e:""+e).replace(Jf,`
`).replace(qf,"")}function Pr(e,t,n){if(t=Qs(t),Qs(e)!==t&&n)throw Error(N(425))}function ll(){}var Ai=null,$i=null;function Ui(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Wi=typeof setTimeout=="function"?setTimeout:void 0,bf=typeof clearTimeout=="function"?clearTimeout:void 0,Ks=typeof Promise=="function"?Promise:void 0,ep=typeof queueMicrotask=="function"?queueMicrotask:typeof Ks<"u"?function(e){return Ks.resolve(null).then(e).catch(tp)}:Wi;function tp(e){setTimeout(function(){throw e})}function ii(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),Xn(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);Xn(t)}function ht(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ys(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var yn=Math.random().toString(36).slice(2),$e="__reactFiber$"+yn,er="__reactProps$"+yn,qe="__reactContainer$"+yn,Vi="__reactEvents$"+yn,np="__reactListeners$"+yn,rp="__reactHandles$"+yn;function _t(e){var t=e[$e];if(t)return t;for(var n=e.parentNode;n;){if(t=n[qe]||n[$e]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ys(e);e!==null;){if(n=e[$e])return n;e=Ys(e)}return t}e=n,n=e.parentNode}return null}function dr(e){return e=e[$e]||e[qe],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Yt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(N(33))}function Cl(e){return e[er]||null}var Hi=[],Xt=-1;function jt(e){return{current:e}}function $(e){0>Xt||(e.current=Hi[Xt],Hi[Xt]=null,Xt--)}function F(e,t){Xt++,Hi[Xt]=e.current,e.current=t}var kt={},se=jt(kt),he=jt(!1),It=kt;function un(e,t){var n=e.type.contextTypes;if(!n)return kt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},i;for(i in n)l[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function xe(e){return e=e.childContextTypes,e!=null}function il(){$(he),$(se)}function Xs(e,t,n){if(se.current!==kt)throw Error(N(168));F(se,t),F(he,n)}function Wu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(N(108,Ud(e)||"Unknown",l));return B({},n,r)}function ol(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||kt,It=se.current,F(se,e),F(he,he.current),!0}function Zs(e,t,n){var r=e.stateNode;if(!r)throw Error(N(169));n?(e=Wu(e,t,It),r.__reactInternalMemoizedMergedChildContext=e,$(he),$(se),F(se,e)):$(he),F(he,n)}var Ge=null,Rl=!1,oi=!1;function Vu(e){Ge===null?Ge=[e]:Ge.push(e)}function lp(e){Rl=!0,Vu(e)}function St(){if(!oi&&Ge!==null){oi=!0;var e=0,t=D;try{var n=Ge;for(D=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ge=null,Rl=!1}catch(l){throw Ge!==null&&(Ge=Ge.slice(e+1)),pu(So,St),l}finally{D=t,oi=!1}}return null}var Zt=[],Jt=0,sl=null,al=0,Se=[],Ee=0,Dt=null,Ke=1,Ye="";function Pt(e,t){Zt[Jt++]=al,Zt[Jt++]=sl,sl=e,al=t}function Hu(e,t,n){Se[Ee++]=Ke,Se[Ee++]=Ye,Se[Ee++]=Dt,Dt=e;var r=Ke;e=Ye;var l=32-Oe(r)-1;r&=~(1<<l),n+=1;var i=32-Oe(t)+l;if(30<i){var o=l-l%5;i=(r&(1<<o)-1).toString(32),r>>=o,l-=o,Ke=1<<32-Oe(t)+l|n<<l|r,Ye=i+e}else Ke=1<<i|n<<l|r,Ye=e}function Mo(e){e.return!==null&&(Pt(e,1),Hu(e,1,0))}function Oo(e){for(;e===sl;)sl=Zt[--Jt],Zt[Jt]=null,al=Zt[--Jt],Zt[Jt]=null;for(;e===Dt;)Dt=Se[--Ee],Se[Ee]=null,Ye=Se[--Ee],Se[Ee]=null,Ke=Se[--Ee],Se[Ee]=null}var we=null,ve=null,U=!1,Me=null;function Bu(e,t){var n=Ce(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Js(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,we=e,ve=ht(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,we=e,ve=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Dt!==null?{id:Ke,overflow:Ye}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ce(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,we=e,ve=null,!0):!1;default:return!1}}function Bi(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Gi(e){if(U){var t=ve;if(t){var n=t;if(!Js(e,t)){if(Bi(e))throw Error(N(418));t=ht(n.nextSibling);var r=we;t&&Js(e,t)?Bu(r,n):(e.flags=e.flags&-4097|2,U=!1,we=e)}}else{if(Bi(e))throw Error(N(418));e.flags=e.flags&-4097|2,U=!1,we=e}}}function qs(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;we=e}function Lr(e){if(e!==we)return!1;if(!U)return qs(e),U=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ui(e.type,e.memoizedProps)),t&&(t=ve)){if(Bi(e))throw Gu(),Error(N(418));for(;t;)Bu(e,t),t=ht(t.nextSibling)}if(qs(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(N(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ve=ht(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ve=null}}else ve=we?ht(e.stateNode.nextSibling):null;return!0}function Gu(){for(var e=ve;e;)e=ht(e.nextSibling)}function cn(){ve=we=null,U=!1}function Io(e){Me===null?Me=[e]:Me.push(e)}var ip=nt.ReactCurrentBatchConfig;function Rn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(N(309));var r=n.stateNode}if(!r)throw Error(N(147,e));var l=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=l.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(N(284));if(!n._owner)throw Error(N(290,e))}return e}function _r(e,t){throw e=Object.prototype.toString.call(t),Error(N(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bs(e){var t=e._init;return t(e._payload)}function Qu(e){function t(f,d){if(e){var p=f.deletions;p===null?(f.deletions=[d],f.flags|=16):p.push(d)}}function n(f,d){if(!e)return null;for(;d!==null;)t(f,d),d=d.sibling;return null}function r(f,d){for(f=new Map;d!==null;)d.key!==null?f.set(d.key,d):f.set(d.index,d),d=d.sibling;return f}function l(f,d){return f=vt(f,d),f.index=0,f.sibling=null,f}function i(f,d,p){return f.index=p,e?(p=f.alternate,p!==null?(p=p.index,p<d?(f.flags|=2,d):p):(f.flags|=2,d)):(f.flags|=1048576,d)}function o(f){return e&&f.alternate===null&&(f.flags|=2),f}function a(f,d,p,k){return d===null||d.tag!==6?(d=pi(p,f.mode,k),d.return=f,d):(d=l(d,p),d.return=f,d)}function u(f,d,p,k){var j=p.type;return j===Bt?h(f,d,p.props.children,k,p.key):d!==null&&(d.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===ot&&bs(j)===d.type)?(k=l(d,p.props),k.ref=Rn(f,d,p),k.return=f,k):(k=Kr(p.type,p.key,p.props,null,f.mode,k),k.ref=Rn(f,d,p),k.return=f,k)}function c(f,d,p,k){return d===null||d.tag!==4||d.stateNode.containerInfo!==p.containerInfo||d.stateNode.implementation!==p.implementation?(d=mi(p,f.mode,k),d.return=f,d):(d=l(d,p.children||[]),d.return=f,d)}function h(f,d,p,k,j){return d===null||d.tag!==7?(d=Ot(p,f.mode,k,j),d.return=f,d):(d=l(d,p),d.return=f,d)}function x(f,d,p){if(typeof d=="string"&&d!==""||typeof d=="number")return d=pi(""+d,f.mode,p),d.return=f,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case vr:return p=Kr(d.type,d.key,d.props,null,f.mode,p),p.ref=Rn(f,null,d),p.return=f,p;case Ht:return d=mi(d,f.mode,p),d.return=f,d;case ot:var k=d._init;return x(f,k(d._payload),p)}if(Tn(d)||Nn(d))return d=Ot(d,f.mode,p,null),d.return=f,d;_r(f,d)}return null}function m(f,d,p,k){var j=d!==null?d.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return j!==null?null:a(f,d,""+p,k);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case vr:return p.key===j?u(f,d,p,k):null;case Ht:return p.key===j?c(f,d,p,k):null;case ot:return j=p._init,m(f,d,j(p._payload),k)}if(Tn(p)||Nn(p))return j!==null?null:h(f,d,p,k,null);_r(f,p)}return null}function w(f,d,p,k,j){if(typeof k=="string"&&k!==""||typeof k=="number")return f=f.get(p)||null,a(d,f,""+k,j);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case vr:return f=f.get(k.key===null?p:k.key)||null,u(d,f,k,j);case Ht:return f=f.get(k.key===null?p:k.key)||null,c(d,f,k,j);case ot:var P=k._init;return w(f,d,p,P(k._payload),j)}if(Tn(k)||Nn(k))return f=f.get(p)||null,h(d,f,k,j,null);_r(d,k)}return null}function g(f,d,p,k){for(var j=null,P=null,R=d,L=d=0,O=null;R!==null&&L<p.length;L++){R.index>L?(O=R,R=null):O=R.sibling;var _=m(f,R,p[L],k);if(_===null){R===null&&(R=O);break}e&&R&&_.alternate===null&&t(f,R),d=i(_,d,L),P===null?j=_:P.sibling=_,P=_,R=O}if(L===p.length)return n(f,R),U&&Pt(f,L),j;if(R===null){for(;L<p.length;L++)R=x(f,p[L],k),R!==null&&(d=i(R,d,L),P===null?j=R:P.sibling=R,P=R);return U&&Pt(f,L),j}for(R=r(f,R);L<p.length;L++)O=w(R,f,L,p[L],k),O!==null&&(e&&O.alternate!==null&&R.delete(O.key===null?L:O.key),d=i(O,d,L),P===null?j=O:P.sibling=O,P=O);return e&&R.forEach(function(fe){return t(f,fe)}),U&&Pt(f,L),j}function v(f,d,p,k){var j=Nn(p);if(typeof j!="function")throw Error(N(150));if(p=j.call(p),p==null)throw Error(N(151));for(var P=j=null,R=d,L=d=0,O=null,_=p.next();R!==null&&!_.done;L++,_=p.next()){R.index>L?(O=R,R=null):O=R.sibling;var fe=m(f,R,_.value,k);if(fe===null){R===null&&(R=O);break}e&&R&&fe.alternate===null&&t(f,R),d=i(fe,d,L),P===null?j=fe:P.sibling=fe,P=fe,R=O}if(_.done)return n(f,R),U&&Pt(f,L),j;if(R===null){for(;!_.done;L++,_=p.next())_=x(f,_.value,k),_!==null&&(d=i(_,d,L),P===null?j=_:P.sibling=_,P=_);return U&&Pt(f,L),j}for(R=r(f,R);!_.done;L++,_=p.next())_=w(R,f,L,_.value,k),_!==null&&(e&&_.alternate!==null&&R.delete(_.key===null?L:_.key),d=i(_,d,L),P===null?j=_:P.sibling=_,P=_);return e&&R.forEach(function(wn){return t(f,wn)}),U&&Pt(f,L),j}function E(f,d,p,k){if(typeof p=="object"&&p!==null&&p.type===Bt&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case vr:e:{for(var j=p.key,P=d;P!==null;){if(P.key===j){if(j=p.type,j===Bt){if(P.tag===7){n(f,P.sibling),d=l(P,p.props.children),d.return=f,f=d;break e}}else if(P.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===ot&&bs(j)===P.type){n(f,P.sibling),d=l(P,p.props),d.ref=Rn(f,P,p),d.return=f,f=d;break e}n(f,P);break}else t(f,P);P=P.sibling}p.type===Bt?(d=Ot(p.props.children,f.mode,k,p.key),d.return=f,f=d):(k=Kr(p.type,p.key,p.props,null,f.mode,k),k.ref=Rn(f,d,p),k.return=f,f=k)}return o(f);case Ht:e:{for(P=p.key;d!==null;){if(d.key===P)if(d.tag===4&&d.stateNode.containerInfo===p.containerInfo&&d.stateNode.implementation===p.implementation){n(f,d.sibling),d=l(d,p.children||[]),d.return=f,f=d;break e}else{n(f,d);break}else t(f,d);d=d.sibling}d=mi(p,f.mode,k),d.return=f,f=d}return o(f);case ot:return P=p._init,E(f,d,P(p._payload),k)}if(Tn(p))return g(f,d,p,k);if(Nn(p))return v(f,d,p,k);_r(f,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,d!==null&&d.tag===6?(n(f,d.sibling),d=l(d,p),d.return=f,f=d):(n(f,d),d=pi(p,f.mode,k),d.return=f,f=d),o(f)):n(f,d)}return E}var dn=Qu(!0),Ku=Qu(!1),ul=jt(null),cl=null,qt=null,Do=null;function Fo(){Do=qt=cl=null}function Ao(e){var t=ul.current;$(ul),e._currentValue=t}function Qi(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function on(e,t){cl=e,Do=qt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(me=!0),e.firstContext=null)}function Pe(e){var t=e._currentValue;if(Do!==e)if(e={context:e,memoizedValue:t,next:null},qt===null){if(cl===null)throw Error(N(308));qt=e,cl.dependencies={lanes:0,firstContext:e}}else qt=qt.next=e;return t}var zt=null;function $o(e){zt===null?zt=[e]:zt.push(e)}function Yu(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,$o(t)):(n.next=l.next,l.next=n),t.interleaved=n,be(e,r)}function be(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var st=!1;function Uo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Xu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Xe(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function xt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,I&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,be(e,n)}return l=r.interleaved,l===null?(t.next=t,$o(r)):(t.next=l.next,l.next=t),r.interleaved=t,be(e,n)}function Wr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Eo(e,n)}}function ea(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?l=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?l=i=t:i=i.next=t}else l=i=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function dl(e,t,n,r){var l=e.updateQueue;st=!1;var i=l.firstBaseUpdate,o=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var u=a,c=u.next;u.next=null,o===null?i=c:o.next=c,o=u;var h=e.alternate;h!==null&&(h=h.updateQueue,a=h.lastBaseUpdate,a!==o&&(a===null?h.firstBaseUpdate=c:a.next=c,h.lastBaseUpdate=u))}if(i!==null){var x=l.baseState;o=0,h=c=u=null,a=i;do{var m=a.lane,w=a.eventTime;if((r&m)===m){h!==null&&(h=h.next={eventTime:w,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var g=e,v=a;switch(m=t,w=n,v.tag){case 1:if(g=v.payload,typeof g=="function"){x=g.call(w,x,m);break e}x=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=v.payload,m=typeof g=="function"?g.call(w,x,m):g,m==null)break e;x=B({},x,m);break e;case 2:st=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,m=l.effects,m===null?l.effects=[a]:m.push(a))}else w={eventTime:w,lane:m,tag:a.tag,payload:a.payload,callback:a.callback,next:null},h===null?(c=h=w,u=x):h=h.next=w,o|=m;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;m=a,a=m.next,m.next=null,l.lastBaseUpdate=m,l.shared.pending=null}}while(!0);if(h===null&&(u=x),l.baseState=u,l.firstBaseUpdate=c,l.lastBaseUpdate=h,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else i===null&&(l.shared.lanes=0);At|=o,e.lanes=o,e.memoizedState=x}}function ta(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(N(191,l));l.call(r)}}}var fr={},We=jt(fr),tr=jt(fr),nr=jt(fr);function Tt(e){if(e===fr)throw Error(N(174));return e}function Wo(e,t){switch(F(nr,t),F(tr,e),F(We,fr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ci(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ci(t,e)}$(We),F(We,t)}function fn(){$(We),$(tr),$(nr)}function Zu(e){Tt(nr.current);var t=Tt(We.current),n=Ci(t,e.type);t!==n&&(F(tr,e),F(We,n))}function Vo(e){tr.current===e&&($(We),$(tr))}var W=jt(0);function fl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var si=[];function Ho(){for(var e=0;e<si.length;e++)si[e]._workInProgressVersionPrimary=null;si.length=0}var Vr=nt.ReactCurrentDispatcher,ai=nt.ReactCurrentBatchConfig,Ft=0,V=null,X=null,q=null,pl=!1,Un=!1,rr=0,op=0;function le(){throw Error(N(321))}function Bo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!De(e[n],t[n]))return!1;return!0}function Go(e,t,n,r,l,i){if(Ft=i,V=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Vr.current=e===null||e.memoizedState===null?cp:dp,e=n(r,l),Un){i=0;do{if(Un=!1,rr=0,25<=i)throw Error(N(301));i+=1,q=X=null,t.updateQueue=null,Vr.current=fp,e=n(r,l)}while(Un)}if(Vr.current=ml,t=X!==null&&X.next!==null,Ft=0,q=X=V=null,pl=!1,t)throw Error(N(300));return e}function Qo(){var e=rr!==0;return rr=0,e}function Ae(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return q===null?V.memoizedState=q=e:q=q.next=e,q}function Le(){if(X===null){var e=V.alternate;e=e!==null?e.memoizedState:null}else e=X.next;var t=q===null?V.memoizedState:q.next;if(t!==null)q=t,X=e;else{if(e===null)throw Error(N(310));X=e,e={memoizedState:X.memoizedState,baseState:X.baseState,baseQueue:X.baseQueue,queue:X.queue,next:null},q===null?V.memoizedState=q=e:q=q.next=e}return q}function lr(e,t){return typeof t=="function"?t(e):t}function ui(e){var t=Le(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=X,l=r.baseQueue,i=n.pending;if(i!==null){if(l!==null){var o=l.next;l.next=i.next,i.next=o}r.baseQueue=l=i,n.pending=null}if(l!==null){i=l.next,r=r.baseState;var a=o=null,u=null,c=i;do{var h=c.lane;if((Ft&h)===h)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var x={lane:h,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=x,o=r):u=u.next=x,V.lanes|=h,At|=h}c=c.next}while(c!==null&&c!==i);u===null?o=r:u.next=a,De(r,t.memoizedState)||(me=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do i=l.lane,V.lanes|=i,At|=i,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ci(e){var t=Le(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,i=t.memoizedState;if(l!==null){n.pending=null;var o=l=l.next;do i=e(i,o.action),o=o.next;while(o!==l);De(i,t.memoizedState)||(me=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Ju(){}function qu(e,t){var n=V,r=Le(),l=t(),i=!De(r.memoizedState,l);if(i&&(r.memoizedState=l,me=!0),r=r.queue,Ko(tc.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||q!==null&&q.memoizedState.tag&1){if(n.flags|=2048,ir(9,ec.bind(null,n,r,l,t),void 0,null),b===null)throw Error(N(349));Ft&30||bu(n,t,l)}return l}function bu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=V.updateQueue,t===null?(t={lastEffect:null,stores:null},V.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ec(e,t,n,r){t.value=n,t.getSnapshot=r,nc(t)&&rc(e)}function tc(e,t,n){return n(function(){nc(t)&&rc(e)})}function nc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!De(e,n)}catch{return!0}}function rc(e){var t=be(e,1);t!==null&&Ie(t,e,1,-1)}function na(e){var t=Ae();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:lr,lastRenderedState:e},t.queue=e,e=e.dispatch=up.bind(null,V,e),[t.memoizedState,e]}function ir(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=V.updateQueue,t===null?(t={lastEffect:null,stores:null},V.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function lc(){return Le().memoizedState}function Hr(e,t,n,r){var l=Ae();V.flags|=e,l.memoizedState=ir(1|t,n,void 0,r===void 0?null:r)}function Pl(e,t,n,r){var l=Le();r=r===void 0?null:r;var i=void 0;if(X!==null){var o=X.memoizedState;if(i=o.destroy,r!==null&&Bo(r,o.deps)){l.memoizedState=ir(t,n,i,r);return}}V.flags|=e,l.memoizedState=ir(1|t,n,i,r)}function ra(e,t){return Hr(8390656,8,e,t)}function Ko(e,t){return Pl(2048,8,e,t)}function ic(e,t){return Pl(4,2,e,t)}function oc(e,t){return Pl(4,4,e,t)}function sc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ac(e,t,n){return n=n!=null?n.concat([e]):null,Pl(4,4,sc.bind(null,t,e),n)}function Yo(){}function uc(e,t){var n=Le();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Bo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function cc(e,t){var n=Le();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Bo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function dc(e,t,n){return Ft&21?(De(n,t)||(n=xu(),V.lanes|=n,At|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,me=!0),e.memoizedState=n)}function sp(e,t){var n=D;D=n!==0&&4>n?n:4,e(!0);var r=ai.transition;ai.transition={};try{e(!1),t()}finally{D=n,ai.transition=r}}function fc(){return Le().memoizedState}function ap(e,t,n){var r=gt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},pc(e))mc(t,n);else if(n=Yu(e,t,n,r),n!==null){var l=ue();Ie(n,e,r,l),hc(n,t,r)}}function up(e,t,n){var r=gt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(pc(e))mc(t,l);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(l.hasEagerState=!0,l.eagerState=a,De(a,o)){var u=t.interleaved;u===null?(l.next=l,$o(t)):(l.next=u.next,u.next=l),t.interleaved=l;return}}catch{}finally{}n=Yu(e,t,l,r),n!==null&&(l=ue(),Ie(n,e,r,l),hc(n,t,r))}}function pc(e){var t=e.alternate;return e===V||t!==null&&t===V}function mc(e,t){Un=pl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function hc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Eo(e,n)}}var ml={readContext:Pe,useCallback:le,useContext:le,useEffect:le,useImperativeHandle:le,useInsertionEffect:le,useLayoutEffect:le,useMemo:le,useReducer:le,useRef:le,useState:le,useDebugValue:le,useDeferredValue:le,useTransition:le,useMutableSource:le,useSyncExternalStore:le,useId:le,unstable_isNewReconciler:!1},cp={readContext:Pe,useCallback:function(e,t){return Ae().memoizedState=[e,t===void 0?null:t],e},useContext:Pe,useEffect:ra,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Hr(4194308,4,sc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Hr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Hr(4,2,e,t)},useMemo:function(e,t){var n=Ae();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ae();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ap.bind(null,V,e),[r.memoizedState,e]},useRef:function(e){var t=Ae();return e={current:e},t.memoizedState=e},useState:na,useDebugValue:Yo,useDeferredValue:function(e){return Ae().memoizedState=e},useTransition:function(){var e=na(!1),t=e[0];return e=sp.bind(null,e[1]),Ae().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=V,l=Ae();if(U){if(n===void 0)throw Error(N(407));n=n()}else{if(n=t(),b===null)throw Error(N(349));Ft&30||bu(r,t,n)}l.memoizedState=n;var i={value:n,getSnapshot:t};return l.queue=i,ra(tc.bind(null,r,i,e),[e]),r.flags|=2048,ir(9,ec.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Ae(),t=b.identifierPrefix;if(U){var n=Ye,r=Ke;n=(r&~(1<<32-Oe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=rr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=op++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},dp={readContext:Pe,useCallback:uc,useContext:Pe,useEffect:Ko,useImperativeHandle:ac,useInsertionEffect:ic,useLayoutEffect:oc,useMemo:cc,useReducer:ui,useRef:lc,useState:function(){return ui(lr)},useDebugValue:Yo,useDeferredValue:function(e){var t=Le();return dc(t,X.memoizedState,e)},useTransition:function(){var e=ui(lr)[0],t=Le().memoizedState;return[e,t]},useMutableSource:Ju,useSyncExternalStore:qu,useId:fc,unstable_isNewReconciler:!1},fp={readContext:Pe,useCallback:uc,useContext:Pe,useEffect:Ko,useImperativeHandle:ac,useInsertionEffect:ic,useLayoutEffect:oc,useMemo:cc,useReducer:ci,useRef:lc,useState:function(){return ci(lr)},useDebugValue:Yo,useDeferredValue:function(e){var t=Le();return X===null?t.memoizedState=e:dc(t,X.memoizedState,e)},useTransition:function(){var e=ci(lr)[0],t=Le().memoizedState;return[e,t]},useMutableSource:Ju,useSyncExternalStore:qu,useId:fc,unstable_isNewReconciler:!1};function ze(e,t){if(e&&e.defaultProps){t=B({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ki(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:B({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ll={isMounted:function(e){return(e=e._reactInternals)?Wt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ue(),l=gt(e),i=Xe(r,l);i.payload=t,n!=null&&(i.callback=n),t=xt(e,i,l),t!==null&&(Ie(t,e,l,r),Wr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ue(),l=gt(e),i=Xe(r,l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=xt(e,i,l),t!==null&&(Ie(t,e,l,r),Wr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ue(),r=gt(e),l=Xe(n,r);l.tag=2,t!=null&&(l.callback=t),t=xt(e,l,r),t!==null&&(Ie(t,e,r,n),Wr(t,e,r))}};function la(e,t,n,r,l,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!Jn(n,r)||!Jn(l,i):!0}function xc(e,t,n){var r=!1,l=kt,i=t.contextType;return typeof i=="object"&&i!==null?i=Pe(i):(l=xe(t)?It:se.current,r=t.contextTypes,i=(r=r!=null)?un(e,l):kt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ll,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=i),t}function ia(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ll.enqueueReplaceState(t,t.state,null)}function Yi(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Uo(e);var i=t.contextType;typeof i=="object"&&i!==null?l.context=Pe(i):(i=xe(t)?It:se.current,l.context=un(e,i)),l.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ki(e,t,i,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Ll.enqueueReplaceState(l,l.state,null),dl(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function pn(e,t){try{var n="",r=t;do n+=$d(r),r=r.return;while(r);var l=n}catch(i){l=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:l,digest:null}}function di(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Xi(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var pp=typeof WeakMap=="function"?WeakMap:Map;function yc(e,t,n){n=Xe(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){xl||(xl=!0,io=r),Xi(e,t)},n}function gc(e,t,n){n=Xe(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Xi(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Xi(e,t),typeof r!="function"&&(yt===null?yt=new Set([this]):yt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function oa(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new pp;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Rp.bind(null,e,t,n),t.then(e,e))}function sa(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function aa(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Xe(-1,1),t.tag=2,xt(n,t,1))),n.lanes|=1),e)}var mp=nt.ReactCurrentOwner,me=!1;function ae(e,t,n,r){t.child=e===null?Ku(t,null,n,r):dn(t,e.child,n,r)}function ua(e,t,n,r,l){n=n.render;var i=t.ref;return on(t,l),r=Go(e,t,n,r,i,l),n=Qo(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,et(e,t,l)):(U&&n&&Mo(t),t.flags|=1,ae(e,t,r,l),t.child)}function ca(e,t,n,r,l){if(e===null){var i=n.type;return typeof i=="function"&&!ns(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,vc(e,t,i,r,l)):(e=Kr(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&l)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:Jn,n(o,r)&&e.ref===t.ref)return et(e,t,l)}return t.flags|=1,e=vt(i,r),e.ref=t.ref,e.return=t,t.child=e}function vc(e,t,n,r,l){if(e!==null){var i=e.memoizedProps;if(Jn(i,r)&&e.ref===t.ref)if(me=!1,t.pendingProps=r=i,(e.lanes&l)!==0)e.flags&131072&&(me=!0);else return t.lanes=e.lanes,et(e,t,l)}return Zi(e,t,n,r,l)}function wc(e,t,n){var r=t.pendingProps,l=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(en,ge),ge|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(en,ge),ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,F(en,ge),ge|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,F(en,ge),ge|=r;return ae(e,t,l,n),t.child}function kc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Zi(e,t,n,r,l){var i=xe(n)?It:se.current;return i=un(t,i),on(t,l),n=Go(e,t,n,r,i,l),r=Qo(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,et(e,t,l)):(U&&r&&Mo(t),t.flags|=1,ae(e,t,n,l),t.child)}function da(e,t,n,r,l){if(xe(n)){var i=!0;ol(t)}else i=!1;if(on(t,l),t.stateNode===null)Br(e,t),xc(t,n,r),Yi(t,n,r,l),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var u=o.context,c=n.contextType;typeof c=="object"&&c!==null?c=Pe(c):(c=xe(n)?It:se.current,c=un(t,c));var h=n.getDerivedStateFromProps,x=typeof h=="function"||typeof o.getSnapshotBeforeUpdate=="function";x||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||u!==c)&&ia(t,o,r,c),st=!1;var m=t.memoizedState;o.state=m,dl(t,r,o,l),u=t.memoizedState,a!==r||m!==u||he.current||st?(typeof h=="function"&&(Ki(t,n,h,r),u=t.memoizedState),(a=st||la(t,n,a,r,m,u,c))?(x||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=c,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Xu(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:ze(t.type,a),o.props=c,x=t.pendingProps,m=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=Pe(u):(u=xe(n)?It:se.current,u=un(t,u));var w=n.getDerivedStateFromProps;(h=typeof w=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==x||m!==u)&&ia(t,o,r,u),st=!1,m=t.memoizedState,o.state=m,dl(t,r,o,l);var g=t.memoizedState;a!==x||m!==g||he.current||st?(typeof w=="function"&&(Ki(t,n,w,r),g=t.memoizedState),(c=st||la(t,n,c,r,m,g,u)||!1)?(h||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,g,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,g,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),o.props=r,o.state=g,o.context=u,r=c):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return Ji(e,t,n,r,i,l)}function Ji(e,t,n,r,l,i){kc(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return l&&Zs(t,n,!1),et(e,t,i);r=t.stateNode,mp.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=dn(t,e.child,null,i),t.child=dn(t,null,a,i)):ae(e,t,a,i),t.memoizedState=r.state,l&&Zs(t,n,!0),t.child}function Nc(e){var t=e.stateNode;t.pendingContext?Xs(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Xs(e,t.context,!1),Wo(e,t.containerInfo)}function fa(e,t,n,r,l){return cn(),Io(l),t.flags|=256,ae(e,t,n,r),t.child}var qi={dehydrated:null,treeContext:null,retryLane:0};function bi(e){return{baseLanes:e,cachePool:null,transitions:null}}function jc(e,t,n){var r=t.pendingProps,l=W.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),F(W,l&1),e===null)return Gi(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Tl(o,r,0,null),e=Ot(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=bi(n),t.memoizedState=qi,e):Xo(t,o));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return hp(e,t,o,r,a,l,n);if(i){i=r.fallback,o=t.mode,l=e.child,a=l.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=vt(l,u),r.subtreeFlags=l.subtreeFlags&14680064),a!==null?i=vt(a,i):(i=Ot(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?bi(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=qi,r}return i=e.child,e=i.sibling,r=vt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Xo(e,t){return t=Tl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function zr(e,t,n,r){return r!==null&&Io(r),dn(t,e.child,null,n),e=Xo(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function hp(e,t,n,r,l,i,o){if(n)return t.flags&256?(t.flags&=-257,r=di(Error(N(422))),zr(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,l=t.mode,r=Tl({mode:"visible",children:r.children},l,0,null),i=Ot(i,l,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&dn(t,e.child,null,o),t.child.memoizedState=bi(o),t.memoizedState=qi,i);if(!(t.mode&1))return zr(e,t,o,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(N(419)),r=di(i,r,void 0),zr(e,t,o,r)}if(a=(o&e.childLanes)!==0,me||a){if(r=b,r!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|o)?0:l,l!==0&&l!==i.retryLane&&(i.retryLane=l,be(e,l),Ie(r,e,l,-1))}return ts(),r=di(Error(N(421))),zr(e,t,o,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Pp.bind(null,e),l._reactRetry=t,null):(e=i.treeContext,ve=ht(l.nextSibling),we=t,U=!0,Me=null,e!==null&&(Se[Ee++]=Ke,Se[Ee++]=Ye,Se[Ee++]=Dt,Ke=e.id,Ye=e.overflow,Dt=t),t=Xo(t,r.children),t.flags|=4096,t)}function pa(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Qi(e.return,t,n)}function fi(e,t,n,r,l){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=l)}function Sc(e,t,n){var r=t.pendingProps,l=r.revealOrder,i=r.tail;if(ae(e,t,r.children,n),r=W.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&pa(e,n,t);else if(e.tag===19)pa(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F(W,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&fl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),fi(t,!1,l,n,i);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&fl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}fi(t,!0,n,null,i);break;case"together":fi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Br(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function et(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),At|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(N(153));if(t.child!==null){for(e=t.child,n=vt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=vt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function xp(e,t,n){switch(t.tag){case 3:Nc(t),cn();break;case 5:Zu(t);break;case 1:xe(t.type)&&ol(t);break;case 4:Wo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;F(ul,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F(W,W.current&1),t.flags|=128,null):n&t.child.childLanes?jc(e,t,n):(F(W,W.current&1),e=et(e,t,n),e!==null?e.sibling:null);F(W,W.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Sc(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),F(W,W.current),r)break;return null;case 22:case 23:return t.lanes=0,wc(e,t,n)}return et(e,t,n)}var Ec,eo,Cc,Rc;Ec=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};eo=function(){};Cc=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Tt(We.current);var i=null;switch(n){case"input":l=Ni(e,l),r=Ni(e,r),i=[];break;case"select":l=B({},l,{value:void 0}),r=B({},r,{value:void 0}),i=[];break;case"textarea":l=Ei(e,l),r=Ei(e,r),i=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ll)}Ri(n,r);var o;n=null;for(c in l)if(!r.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var a=l[c];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Bn.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(a=l!=null?l[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(o in a)!a.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&a[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(i=i||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Bn.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&A("scroll",e),i||a===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};Rc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Pn(e,t){if(!U)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ie(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function yp(e,t,n){var r=t.pendingProps;switch(Oo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ie(t),null;case 1:return xe(t.type)&&il(),ie(t),null;case 3:return r=t.stateNode,fn(),$(he),$(se),Ho(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Lr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Me!==null&&(ao(Me),Me=null))),eo(e,t),ie(t),null;case 5:Vo(t);var l=Tt(nr.current);if(n=t.type,e!==null&&t.stateNode!=null)Cc(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(N(166));return ie(t),null}if(e=Tt(We.current),Lr(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[$e]=t,r[er]=i,e=(t.mode&1)!==0,n){case"dialog":A("cancel",r),A("close",r);break;case"iframe":case"object":case"embed":A("load",r);break;case"video":case"audio":for(l=0;l<On.length;l++)A(On[l],r);break;case"source":A("error",r);break;case"img":case"image":case"link":A("error",r),A("load",r);break;case"details":A("toggle",r);break;case"input":Ns(r,i),A("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},A("invalid",r);break;case"textarea":Ss(r,i),A("invalid",r)}Ri(n,i),l=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Pr(r.textContent,a,e),l=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Pr(r.textContent,a,e),l=["children",""+a]):Bn.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&A("scroll",r)}switch(n){case"input":wr(r),js(r,i,!0);break;case"textarea":wr(r),Es(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ll)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=tu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[$e]=t,e[er]=r,Ec(e,t,!1,!1),t.stateNode=e;e:{switch(o=Pi(n,r),n){case"dialog":A("cancel",e),A("close",e),l=r;break;case"iframe":case"object":case"embed":A("load",e),l=r;break;case"video":case"audio":for(l=0;l<On.length;l++)A(On[l],e);l=r;break;case"source":A("error",e),l=r;break;case"img":case"image":case"link":A("error",e),A("load",e),l=r;break;case"details":A("toggle",e),l=r;break;case"input":Ns(e,r),l=Ni(e,r),A("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=B({},r,{value:void 0}),A("invalid",e);break;case"textarea":Ss(e,r),l=Ei(e,r),A("invalid",e);break;default:l=r}Ri(n,l),a=l;for(i in a)if(a.hasOwnProperty(i)){var u=a[i];i==="style"?lu(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&nu(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Gn(e,u):typeof u=="number"&&Gn(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Bn.hasOwnProperty(i)?u!=null&&i==="onScroll"&&A("scroll",e):u!=null&&vo(e,i,u,o))}switch(n){case"input":wr(e),js(e,r,!1);break;case"textarea":wr(e),Es(e);break;case"option":r.value!=null&&e.setAttribute("value",""+wt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?tn(e,!!r.multiple,i,!1):r.defaultValue!=null&&tn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=ll)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ie(t),null;case 6:if(e&&t.stateNode!=null)Rc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(N(166));if(n=Tt(nr.current),Tt(We.current),Lr(t)){if(r=t.stateNode,n=t.memoizedProps,r[$e]=t,(i=r.nodeValue!==n)&&(e=we,e!==null))switch(e.tag){case 3:Pr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Pr(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[$e]=t,t.stateNode=r}return ie(t),null;case 13:if($(W),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(U&&ve!==null&&t.mode&1&&!(t.flags&128))Gu(),cn(),t.flags|=98560,i=!1;else if(i=Lr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(N(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(N(317));i[$e]=t}else cn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ie(t),i=!1}else Me!==null&&(ao(Me),Me=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||W.current&1?Z===0&&(Z=3):ts())),t.updateQueue!==null&&(t.flags|=4),ie(t),null);case 4:return fn(),eo(e,t),e===null&&qn(t.stateNode.containerInfo),ie(t),null;case 10:return Ao(t.type._context),ie(t),null;case 17:return xe(t.type)&&il(),ie(t),null;case 19:if($(W),i=t.memoizedState,i===null)return ie(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)Pn(i,!1);else{if(Z!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=fl(e),o!==null){for(t.flags|=128,Pn(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F(W,W.current&1|2),t.child}e=e.sibling}i.tail!==null&&K()>mn&&(t.flags|=128,r=!0,Pn(i,!1),t.lanes=4194304)}else{if(!r)if(e=fl(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Pn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!U)return ie(t),null}else 2*K()-i.renderingStartTime>mn&&n!==1073741824&&(t.flags|=128,r=!0,Pn(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=K(),t.sibling=null,n=W.current,F(W,r?n&1|2:n&1),t):(ie(t),null);case 22:case 23:return es(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ge&1073741824&&(ie(t),t.subtreeFlags&6&&(t.flags|=8192)):ie(t),null;case 24:return null;case 25:return null}throw Error(N(156,t.tag))}function gp(e,t){switch(Oo(t),t.tag){case 1:return xe(t.type)&&il(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fn(),$(he),$(se),Ho(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Vo(t),null;case 13:if($(W),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(N(340));cn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(W),null;case 4:return fn(),null;case 10:return Ao(t.type._context),null;case 22:case 23:return es(),null;case 24:return null;default:return null}}var Tr=!1,oe=!1,vp=typeof WeakSet=="function"?WeakSet:Set,S=null;function bt(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){G(e,t,r)}else n.current=null}function to(e,t,n){try{n()}catch(r){G(e,t,r)}}var ma=!1;function wp(e,t){if(Ai=tl,e=Tu(),To(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,u=-1,c=0,h=0,x=e,m=null;t:for(;;){for(var w;x!==n||l!==0&&x.nodeType!==3||(a=o+l),x!==i||r!==0&&x.nodeType!==3||(u=o+r),x.nodeType===3&&(o+=x.nodeValue.length),(w=x.firstChild)!==null;)m=x,x=w;for(;;){if(x===e)break t;if(m===n&&++c===l&&(a=o),m===i&&++h===r&&(u=o),(w=x.nextSibling)!==null)break;x=m,m=x.parentNode}x=w}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for($i={focusedElem:e,selectionRange:n},tl=!1,S=t;S!==null;)if(t=S,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,S=e;else for(;S!==null;){t=S;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var v=g.memoizedProps,E=g.memoizedState,f=t.stateNode,d=f.getSnapshotBeforeUpdate(t.elementType===t.type?v:ze(t.type,v),E);f.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(k){G(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,S=e;break}S=t.return}return g=ma,ma=!1,g}function Wn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var i=l.destroy;l.destroy=void 0,i!==void 0&&to(t,n,i)}l=l.next}while(l!==r)}}function _l(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function no(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Pc(e){var t=e.alternate;t!==null&&(e.alternate=null,Pc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$e],delete t[er],delete t[Vi],delete t[np],delete t[rp])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Lc(e){return e.tag===5||e.tag===3||e.tag===4}function ha(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Lc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ro(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ll));else if(r!==4&&(e=e.child,e!==null))for(ro(e,t,n),e=e.sibling;e!==null;)ro(e,t,n),e=e.sibling}function lo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(lo(e,t,n),e=e.sibling;e!==null;)lo(e,t,n),e=e.sibling}var ee=null,Te=!1;function lt(e,t,n){for(n=n.child;n!==null;)_c(e,t,n),n=n.sibling}function _c(e,t,n){if(Ue&&typeof Ue.onCommitFiberUnmount=="function")try{Ue.onCommitFiberUnmount(Nl,n)}catch{}switch(n.tag){case 5:oe||bt(n,t);case 6:var r=ee,l=Te;ee=null,lt(e,t,n),ee=r,Te=l,ee!==null&&(Te?(e=ee,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ee.removeChild(n.stateNode));break;case 18:ee!==null&&(Te?(e=ee,n=n.stateNode,e.nodeType===8?ii(e.parentNode,n):e.nodeType===1&&ii(e,n),Xn(e)):ii(ee,n.stateNode));break;case 4:r=ee,l=Te,ee=n.stateNode.containerInfo,Te=!0,lt(e,t,n),ee=r,Te=l;break;case 0:case 11:case 14:case 15:if(!oe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var i=l,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&to(n,t,o),l=l.next}while(l!==r)}lt(e,t,n);break;case 1:if(!oe&&(bt(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){G(n,t,a)}lt(e,t,n);break;case 21:lt(e,t,n);break;case 22:n.mode&1?(oe=(r=oe)||n.memoizedState!==null,lt(e,t,n),oe=r):lt(e,t,n);break;default:lt(e,t,n)}}function xa(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new vp),t.forEach(function(r){var l=Lp.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function _e(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:ee=a.stateNode,Te=!1;break e;case 3:ee=a.stateNode.containerInfo,Te=!0;break e;case 4:ee=a.stateNode.containerInfo,Te=!0;break e}a=a.return}if(ee===null)throw Error(N(160));_c(i,o,l),ee=null,Te=!1;var u=l.alternate;u!==null&&(u.return=null),l.return=null}catch(c){G(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)zc(t,e),t=t.sibling}function zc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(_e(t,e),Fe(e),r&4){try{Wn(3,e,e.return),_l(3,e)}catch(v){G(e,e.return,v)}try{Wn(5,e,e.return)}catch(v){G(e,e.return,v)}}break;case 1:_e(t,e),Fe(e),r&512&&n!==null&&bt(n,n.return);break;case 5:if(_e(t,e),Fe(e),r&512&&n!==null&&bt(n,n.return),e.flags&32){var l=e.stateNode;try{Gn(l,"")}catch(v){G(e,e.return,v)}}if(r&4&&(l=e.stateNode,l!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&ba(l,i),Pi(a,o);var c=Pi(a,i);for(o=0;o<u.length;o+=2){var h=u[o],x=u[o+1];h==="style"?lu(l,x):h==="dangerouslySetInnerHTML"?nu(l,x):h==="children"?Gn(l,x):vo(l,h,x,c)}switch(a){case"input":ji(l,i);break;case"textarea":eu(l,i);break;case"select":var m=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!i.multiple;var w=i.value;w!=null?tn(l,!!i.multiple,w,!1):m!==!!i.multiple&&(i.defaultValue!=null?tn(l,!!i.multiple,i.defaultValue,!0):tn(l,!!i.multiple,i.multiple?[]:"",!1))}l[er]=i}catch(v){G(e,e.return,v)}}break;case 6:if(_e(t,e),Fe(e),r&4){if(e.stateNode===null)throw Error(N(162));l=e.stateNode,i=e.memoizedProps;try{l.nodeValue=i}catch(v){G(e,e.return,v)}}break;case 3:if(_e(t,e),Fe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Xn(t.containerInfo)}catch(v){G(e,e.return,v)}break;case 4:_e(t,e),Fe(e);break;case 13:_e(t,e),Fe(e),l=e.child,l.flags&8192&&(i=l.memoizedState!==null,l.stateNode.isHidden=i,!i||l.alternate!==null&&l.alternate.memoizedState!==null||(qo=K())),r&4&&xa(e);break;case 22:if(h=n!==null&&n.memoizedState!==null,e.mode&1?(oe=(c=oe)||h,_e(t,e),oe=c):_e(t,e),Fe(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!h&&e.mode&1)for(S=e,h=e.child;h!==null;){for(x=S=h;S!==null;){switch(m=S,w=m.child,m.tag){case 0:case 11:case 14:case 15:Wn(4,m,m.return);break;case 1:bt(m,m.return);var g=m.stateNode;if(typeof g.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(v){G(r,n,v)}}break;case 5:bt(m,m.return);break;case 22:if(m.memoizedState!==null){ga(x);continue}}w!==null?(w.return=m,S=w):ga(x)}h=h.sibling}e:for(h=null,x=e;;){if(x.tag===5){if(h===null){h=x;try{l=x.stateNode,c?(i=l.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=x.stateNode,u=x.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=ru("display",o))}catch(v){G(e,e.return,v)}}}else if(x.tag===6){if(h===null)try{x.stateNode.nodeValue=c?"":x.memoizedProps}catch(v){G(e,e.return,v)}}else if((x.tag!==22&&x.tag!==23||x.memoizedState===null||x===e)&&x.child!==null){x.child.return=x,x=x.child;continue}if(x===e)break e;for(;x.sibling===null;){if(x.return===null||x.return===e)break e;h===x&&(h=null),x=x.return}h===x&&(h=null),x.sibling.return=x.return,x=x.sibling}}break;case 19:_e(t,e),Fe(e),r&4&&xa(e);break;case 21:break;default:_e(t,e),Fe(e)}}function Fe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Lc(n)){var r=n;break e}n=n.return}throw Error(N(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Gn(l,""),r.flags&=-33);var i=ha(e);lo(e,i,l);break;case 3:case 4:var o=r.stateNode.containerInfo,a=ha(e);ro(e,a,o);break;default:throw Error(N(161))}}catch(u){G(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function kp(e,t,n){S=e,Tc(e)}function Tc(e,t,n){for(var r=(e.mode&1)!==0;S!==null;){var l=S,i=l.child;if(l.tag===22&&r){var o=l.memoizedState!==null||Tr;if(!o){var a=l.alternate,u=a!==null&&a.memoizedState!==null||oe;a=Tr;var c=oe;if(Tr=o,(oe=u)&&!c)for(S=l;S!==null;)o=S,u=o.child,o.tag===22&&o.memoizedState!==null?va(l):u!==null?(u.return=o,S=u):va(l);for(;i!==null;)S=i,Tc(i),i=i.sibling;S=l,Tr=a,oe=c}ya(e)}else l.subtreeFlags&8772&&i!==null?(i.return=l,S=i):ya(e)}}function ya(e){for(;S!==null;){var t=S;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:oe||_l(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!oe)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:ze(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&ta(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ta(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var h=c.memoizedState;if(h!==null){var x=h.dehydrated;x!==null&&Xn(x)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}oe||t.flags&512&&no(t)}catch(m){G(t,t.return,m)}}if(t===e){S=null;break}if(n=t.sibling,n!==null){n.return=t.return,S=n;break}S=t.return}}function ga(e){for(;S!==null;){var t=S;if(t===e){S=null;break}var n=t.sibling;if(n!==null){n.return=t.return,S=n;break}S=t.return}}function va(e){for(;S!==null;){var t=S;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{_l(4,t)}catch(u){G(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(u){G(t,l,u)}}var i=t.return;try{no(t)}catch(u){G(t,i,u)}break;case 5:var o=t.return;try{no(t)}catch(u){G(t,o,u)}}}catch(u){G(t,t.return,u)}if(t===e){S=null;break}var a=t.sibling;if(a!==null){a.return=t.return,S=a;break}S=t.return}}var Np=Math.ceil,hl=nt.ReactCurrentDispatcher,Zo=nt.ReactCurrentOwner,Re=nt.ReactCurrentBatchConfig,I=0,b=null,Y=null,te=0,ge=0,en=jt(0),Z=0,or=null,At=0,zl=0,Jo=0,Vn=null,pe=null,qo=0,mn=1/0,Be=null,xl=!1,io=null,yt=null,Mr=!1,dt=null,yl=0,Hn=0,oo=null,Gr=-1,Qr=0;function ue(){return I&6?K():Gr!==-1?Gr:Gr=K()}function gt(e){return e.mode&1?I&2&&te!==0?te&-te:ip.transition!==null?(Qr===0&&(Qr=xu()),Qr):(e=D,e!==0||(e=window.event,e=e===void 0?16:ju(e.type)),e):1}function Ie(e,t,n,r){if(50<Hn)throw Hn=0,oo=null,Error(N(185));ur(e,n,r),(!(I&2)||e!==b)&&(e===b&&(!(I&2)&&(zl|=n),Z===4&&ut(e,te)),ye(e,r),n===1&&I===0&&!(t.mode&1)&&(mn=K()+500,Rl&&St()))}function ye(e,t){var n=e.callbackNode;lf(e,t);var r=el(e,e===b?te:0);if(r===0)n!==null&&Ps(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ps(n),t===1)e.tag===0?lp(wa.bind(null,e)):Vu(wa.bind(null,e)),ep(function(){!(I&6)&&St()}),n=null;else{switch(yu(r)){case 1:n=So;break;case 4:n=mu;break;case 16:n=br;break;case 536870912:n=hu;break;default:n=br}n=Uc(n,Mc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Mc(e,t){if(Gr=-1,Qr=0,I&6)throw Error(N(327));var n=e.callbackNode;if(sn()&&e.callbackNode!==n)return null;var r=el(e,e===b?te:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=gl(e,r);else{t=r;var l=I;I|=2;var i=Ic();(b!==e||te!==t)&&(Be=null,mn=K()+500,Mt(e,t));do try{Ep();break}catch(a){Oc(e,a)}while(!0);Fo(),hl.current=i,I=l,Y!==null?t=0:(b=null,te=0,t=Z)}if(t!==0){if(t===2&&(l=Mi(e),l!==0&&(r=l,t=so(e,l))),t===1)throw n=or,Mt(e,0),ut(e,r),ye(e,K()),n;if(t===6)ut(e,r);else{if(l=e.current.alternate,!(r&30)&&!jp(l)&&(t=gl(e,r),t===2&&(i=Mi(e),i!==0&&(r=i,t=so(e,i))),t===1))throw n=or,Mt(e,0),ut(e,r),ye(e,K()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(N(345));case 2:Lt(e,pe,Be);break;case 3:if(ut(e,r),(r&130023424)===r&&(t=qo+500-K(),10<t)){if(el(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){ue(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Wi(Lt.bind(null,e,pe,Be),t);break}Lt(e,pe,Be);break;case 4:if(ut(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var o=31-Oe(r);i=1<<o,o=t[o],o>l&&(l=o),r&=~i}if(r=l,r=K()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Np(r/1960))-r,10<r){e.timeoutHandle=Wi(Lt.bind(null,e,pe,Be),r);break}Lt(e,pe,Be);break;case 5:Lt(e,pe,Be);break;default:throw Error(N(329))}}}return ye(e,K()),e.callbackNode===n?Mc.bind(null,e):null}function so(e,t){var n=Vn;return e.current.memoizedState.isDehydrated&&(Mt(e,t).flags|=256),e=gl(e,t),e!==2&&(t=pe,pe=n,t!==null&&ao(t)),e}function ao(e){pe===null?pe=e:pe.push.apply(pe,e)}function jp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],i=l.getSnapshot;l=l.value;try{if(!De(i(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ut(e,t){for(t&=~Jo,t&=~zl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Oe(t),r=1<<n;e[n]=-1,t&=~r}}function wa(e){if(I&6)throw Error(N(327));sn();var t=el(e,0);if(!(t&1))return ye(e,K()),null;var n=gl(e,t);if(e.tag!==0&&n===2){var r=Mi(e);r!==0&&(t=r,n=so(e,r))}if(n===1)throw n=or,Mt(e,0),ut(e,t),ye(e,K()),n;if(n===6)throw Error(N(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Lt(e,pe,Be),ye(e,K()),null}function bo(e,t){var n=I;I|=1;try{return e(t)}finally{I=n,I===0&&(mn=K()+500,Rl&&St())}}function $t(e){dt!==null&&dt.tag===0&&!(I&6)&&sn();var t=I;I|=1;var n=Re.transition,r=D;try{if(Re.transition=null,D=1,e)return e()}finally{D=r,Re.transition=n,I=t,!(I&6)&&St()}}function es(){ge=en.current,$(en)}function Mt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,bf(n)),Y!==null)for(n=Y.return;n!==null;){var r=n;switch(Oo(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&il();break;case 3:fn(),$(he),$(se),Ho();break;case 5:Vo(r);break;case 4:fn();break;case 13:$(W);break;case 19:$(W);break;case 10:Ao(r.type._context);break;case 22:case 23:es()}n=n.return}if(b=e,Y=e=vt(e.current,null),te=ge=t,Z=0,or=null,Jo=zl=At=0,pe=Vn=null,zt!==null){for(t=0;t<zt.length;t++)if(n=zt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=l,r.next=o}n.pending=r}zt=null}return e}function Oc(e,t){do{var n=Y;try{if(Fo(),Vr.current=ml,pl){for(var r=V.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}pl=!1}if(Ft=0,q=X=V=null,Un=!1,rr=0,Zo.current=null,n===null||n.return===null){Z=1,or=t,Y=null;break}e:{var i=e,o=n.return,a=n,u=t;if(t=te,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,h=a,x=h.tag;if(!(h.mode&1)&&(x===0||x===11||x===15)){var m=h.alternate;m?(h.updateQueue=m.updateQueue,h.memoizedState=m.memoizedState,h.lanes=m.lanes):(h.updateQueue=null,h.memoizedState=null)}var w=sa(o);if(w!==null){w.flags&=-257,aa(w,o,a,i,t),w.mode&1&&oa(i,c,t),t=w,u=c;var g=t.updateQueue;if(g===null){var v=new Set;v.add(u),t.updateQueue=v}else g.add(u);break e}else{if(!(t&1)){oa(i,c,t),ts();break e}u=Error(N(426))}}else if(U&&a.mode&1){var E=sa(o);if(E!==null){!(E.flags&65536)&&(E.flags|=256),aa(E,o,a,i,t),Io(pn(u,a));break e}}i=u=pn(u,a),Z!==4&&(Z=2),Vn===null?Vn=[i]:Vn.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var f=yc(i,u,t);ea(i,f);break e;case 1:a=u;var d=i.type,p=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(yt===null||!yt.has(p)))){i.flags|=65536,t&=-t,i.lanes|=t;var k=gc(i,a,t);ea(i,k);break e}}i=i.return}while(i!==null)}Fc(n)}catch(j){t=j,Y===n&&n!==null&&(Y=n=n.return);continue}break}while(!0)}function Ic(){var e=hl.current;return hl.current=ml,e===null?ml:e}function ts(){(Z===0||Z===3||Z===2)&&(Z=4),b===null||!(At&268435455)&&!(zl&268435455)||ut(b,te)}function gl(e,t){var n=I;I|=2;var r=Ic();(b!==e||te!==t)&&(Be=null,Mt(e,t));do try{Sp();break}catch(l){Oc(e,l)}while(!0);if(Fo(),I=n,hl.current=r,Y!==null)throw Error(N(261));return b=null,te=0,Z}function Sp(){for(;Y!==null;)Dc(Y)}function Ep(){for(;Y!==null&&!Xd();)Dc(Y)}function Dc(e){var t=$c(e.alternate,e,ge);e.memoizedProps=e.pendingProps,t===null?Fc(e):Y=t,Zo.current=null}function Fc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=gp(n,t),n!==null){n.flags&=32767,Y=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Z=6,Y=null;return}}else if(n=yp(n,t,ge),n!==null){Y=n;return}if(t=t.sibling,t!==null){Y=t;return}Y=t=e}while(t!==null);Z===0&&(Z=5)}function Lt(e,t,n){var r=D,l=Re.transition;try{Re.transition=null,D=1,Cp(e,t,n,r)}finally{Re.transition=l,D=r}return null}function Cp(e,t,n,r){do sn();while(dt!==null);if(I&6)throw Error(N(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(N(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(of(e,i),e===b&&(Y=b=null,te=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Mr||(Mr=!0,Uc(br,function(){return sn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Re.transition,Re.transition=null;var o=D;D=1;var a=I;I|=4,Zo.current=null,wp(e,n),zc(n,e),Qf($i),tl=!!Ai,$i=Ai=null,e.current=n,kp(n),Zd(),I=a,D=o,Re.transition=i}else e.current=n;if(Mr&&(Mr=!1,dt=e,yl=l),i=e.pendingLanes,i===0&&(yt=null),bd(n.stateNode),ye(e,K()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(xl)throw xl=!1,e=io,io=null,e;return yl&1&&e.tag!==0&&sn(),i=e.pendingLanes,i&1?e===oo?Hn++:(Hn=0,oo=e):Hn=0,St(),null}function sn(){if(dt!==null){var e=yu(yl),t=Re.transition,n=D;try{if(Re.transition=null,D=16>e?16:e,dt===null)var r=!1;else{if(e=dt,dt=null,yl=0,I&6)throw Error(N(331));var l=I;for(I|=4,S=e.current;S!==null;){var i=S,o=i.child;if(S.flags&16){var a=i.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(S=c;S!==null;){var h=S;switch(h.tag){case 0:case 11:case 15:Wn(8,h,i)}var x=h.child;if(x!==null)x.return=h,S=x;else for(;S!==null;){h=S;var m=h.sibling,w=h.return;if(Pc(h),h===c){S=null;break}if(m!==null){m.return=w,S=m;break}S=w}}}var g=i.alternate;if(g!==null){var v=g.child;if(v!==null){g.child=null;do{var E=v.sibling;v.sibling=null,v=E}while(v!==null)}}S=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,S=o;else e:for(;S!==null;){if(i=S,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Wn(9,i,i.return)}var f=i.sibling;if(f!==null){f.return=i.return,S=f;break e}S=i.return}}var d=e.current;for(S=d;S!==null;){o=S;var p=o.child;if(o.subtreeFlags&2064&&p!==null)p.return=o,S=p;else e:for(o=d;S!==null;){if(a=S,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:_l(9,a)}}catch(j){G(a,a.return,j)}if(a===o){S=null;break e}var k=a.sibling;if(k!==null){k.return=a.return,S=k;break e}S=a.return}}if(I=l,St(),Ue&&typeof Ue.onPostCommitFiberRoot=="function")try{Ue.onPostCommitFiberRoot(Nl,e)}catch{}r=!0}return r}finally{D=n,Re.transition=t}}return!1}function ka(e,t,n){t=pn(n,t),t=yc(e,t,1),e=xt(e,t,1),t=ue(),e!==null&&(ur(e,1,t),ye(e,t))}function G(e,t,n){if(e.tag===3)ka(e,e,n);else for(;t!==null;){if(t.tag===3){ka(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(yt===null||!yt.has(r))){e=pn(n,e),e=gc(t,e,1),t=xt(t,e,1),e=ue(),t!==null&&(ur(t,1,e),ye(t,e));break}}t=t.return}}function Rp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ue(),e.pingedLanes|=e.suspendedLanes&n,b===e&&(te&n)===n&&(Z===4||Z===3&&(te&130023424)===te&&500>K()-qo?Mt(e,0):Jo|=n),ye(e,t)}function Ac(e,t){t===0&&(e.mode&1?(t=jr,jr<<=1,!(jr&130023424)&&(jr=4194304)):t=1);var n=ue();e=be(e,t),e!==null&&(ur(e,t,n),ye(e,n))}function Pp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ac(e,n)}function Lp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(N(314))}r!==null&&r.delete(t),Ac(e,n)}var $c;$c=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||he.current)me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return me=!1,xp(e,t,n);me=!!(e.flags&131072)}else me=!1,U&&t.flags&1048576&&Hu(t,al,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Br(e,t),e=t.pendingProps;var l=un(t,se.current);on(t,n),l=Go(null,t,r,e,l,n);var i=Qo();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,xe(r)?(i=!0,ol(t)):i=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Uo(t),l.updater=Ll,t.stateNode=l,l._reactInternals=t,Yi(t,r,e,n),t=Ji(null,t,r,!0,i,n)):(t.tag=0,U&&i&&Mo(t),ae(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Br(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=zp(r),e=ze(r,e),l){case 0:t=Zi(null,t,r,e,n);break e;case 1:t=da(null,t,r,e,n);break e;case 11:t=ua(null,t,r,e,n);break e;case 14:t=ca(null,t,r,ze(r.type,e),n);break e}throw Error(N(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),Zi(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),da(e,t,r,l,n);case 3:e:{if(Nc(t),e===null)throw Error(N(387));r=t.pendingProps,i=t.memoizedState,l=i.element,Xu(e,t),dl(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){l=pn(Error(N(423)),t),t=fa(e,t,r,n,l);break e}else if(r!==l){l=pn(Error(N(424)),t),t=fa(e,t,r,n,l);break e}else for(ve=ht(t.stateNode.containerInfo.firstChild),we=t,U=!0,Me=null,n=Ku(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(cn(),r===l){t=et(e,t,n);break e}ae(e,t,r,n)}t=t.child}return t;case 5:return Zu(t),e===null&&Gi(t),r=t.type,l=t.pendingProps,i=e!==null?e.memoizedProps:null,o=l.children,Ui(r,l)?o=null:i!==null&&Ui(r,i)&&(t.flags|=32),kc(e,t),ae(e,t,o,n),t.child;case 6:return e===null&&Gi(t),null;case 13:return jc(e,t,n);case 4:return Wo(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=dn(t,null,r,n):ae(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),ua(e,t,r,l,n);case 7:return ae(e,t,t.pendingProps,n),t.child;case 8:return ae(e,t,t.pendingProps.children,n),t.child;case 12:return ae(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,i=t.memoizedProps,o=l.value,F(ul,r._currentValue),r._currentValue=o,i!==null)if(De(i.value,o)){if(i.children===l.children&&!he.current){t=et(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=Xe(-1,n&-n),u.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var h=c.pending;h===null?u.next=u:(u.next=h.next,h.next=u),c.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),Qi(i.return,n,t),a.lanes|=n;break}u=u.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(N(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Qi(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}ae(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,on(t,n),l=Pe(l),r=r(l),t.flags|=1,ae(e,t,r,n),t.child;case 14:return r=t.type,l=ze(r,t.pendingProps),l=ze(r.type,l),ca(e,t,r,l,n);case 15:return vc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),Br(e,t),t.tag=1,xe(r)?(e=!0,ol(t)):e=!1,on(t,n),xc(t,r,l),Yi(t,r,l,n),Ji(null,t,r,!0,e,n);case 19:return Sc(e,t,n);case 22:return wc(e,t,n)}throw Error(N(156,t.tag))};function Uc(e,t){return pu(e,t)}function _p(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ce(e,t,n,r){return new _p(e,t,n,r)}function ns(e){return e=e.prototype,!(!e||!e.isReactComponent)}function zp(e){if(typeof e=="function")return ns(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ko)return 11;if(e===No)return 14}return 2}function vt(e,t){var n=e.alternate;return n===null?(n=Ce(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Kr(e,t,n,r,l,i){var o=2;if(r=e,typeof e=="function")ns(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Bt:return Ot(n.children,l,i,t);case wo:o=8,l|=8;break;case gi:return e=Ce(12,n,t,l|2),e.elementType=gi,e.lanes=i,e;case vi:return e=Ce(13,n,t,l),e.elementType=vi,e.lanes=i,e;case wi:return e=Ce(19,n,t,l),e.elementType=wi,e.lanes=i,e;case Za:return Tl(n,l,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ya:o=10;break e;case Xa:o=9;break e;case ko:o=11;break e;case No:o=14;break e;case ot:o=16,r=null;break e}throw Error(N(130,e==null?e:typeof e,""))}return t=Ce(o,n,t,l),t.elementType=e,t.type=r,t.lanes=i,t}function Ot(e,t,n,r){return e=Ce(7,e,r,t),e.lanes=n,e}function Tl(e,t,n,r){return e=Ce(22,e,r,t),e.elementType=Za,e.lanes=n,e.stateNode={isHidden:!1},e}function pi(e,t,n){return e=Ce(6,e,null,t),e.lanes=n,e}function mi(e,t,n){return t=Ce(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Tp(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Yl(0),this.expirationTimes=Yl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Yl(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function rs(e,t,n,r,l,i,o,a,u){return e=new Tp(e,t,n,a,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ce(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Uo(i),e}function Mp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ht,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Wc(e){if(!e)return kt;e=e._reactInternals;e:{if(Wt(e)!==e||e.tag!==1)throw Error(N(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(xe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(N(171))}if(e.tag===1){var n=e.type;if(xe(n))return Wu(e,n,t)}return t}function Vc(e,t,n,r,l,i,o,a,u){return e=rs(n,r,!0,e,l,i,o,a,u),e.context=Wc(null),n=e.current,r=ue(),l=gt(n),i=Xe(r,l),i.callback=t??null,xt(n,i,l),e.current.lanes=l,ur(e,l,r),ye(e,r),e}function Ml(e,t,n,r){var l=t.current,i=ue(),o=gt(l);return n=Wc(n),t.context===null?t.context=n:t.pendingContext=n,t=Xe(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=xt(l,t,o),e!==null&&(Ie(e,l,o,i),Wr(e,l,o)),o}function vl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Na(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ls(e,t){Na(e,t),(e=e.alternate)&&Na(e,t)}function Op(){return null}var Hc=typeof reportError=="function"?reportError:function(e){console.error(e)};function is(e){this._internalRoot=e}Ol.prototype.render=is.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(N(409));Ml(e,t,null,null)};Ol.prototype.unmount=is.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;$t(function(){Ml(null,e,null,null)}),t[qe]=null}};function Ol(e){this._internalRoot=e}Ol.prototype.unstable_scheduleHydration=function(e){if(e){var t=wu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<at.length&&t!==0&&t<at[n].priority;n++);at.splice(n,0,e),n===0&&Nu(e)}};function os(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Il(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ja(){}function Ip(e,t,n,r,l){if(l){if(typeof r=="function"){var i=r;r=function(){var c=vl(o);i.call(c)}}var o=Vc(t,r,e,0,null,!1,!1,"",ja);return e._reactRootContainer=o,e[qe]=o.current,qn(e.nodeType===8?e.parentNode:e),$t(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var a=r;r=function(){var c=vl(u);a.call(c)}}var u=rs(e,0,!1,null,null,!1,!1,"",ja);return e._reactRootContainer=u,e[qe]=u.current,qn(e.nodeType===8?e.parentNode:e),$t(function(){Ml(t,u,n,r)}),u}function Dl(e,t,n,r,l){var i=n._reactRootContainer;if(i){var o=i;if(typeof l=="function"){var a=l;l=function(){var u=vl(o);a.call(u)}}Ml(t,o,e,l)}else o=Ip(n,t,e,l,r);return vl(o)}gu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Mn(t.pendingLanes);n!==0&&(Eo(t,n|1),ye(t,K()),!(I&6)&&(mn=K()+500,St()))}break;case 13:$t(function(){var r=be(e,1);if(r!==null){var l=ue();Ie(r,e,1,l)}}),ls(e,1)}};Co=function(e){if(e.tag===13){var t=be(e,134217728);if(t!==null){var n=ue();Ie(t,e,134217728,n)}ls(e,134217728)}};vu=function(e){if(e.tag===13){var t=gt(e),n=be(e,t);if(n!==null){var r=ue();Ie(n,e,t,r)}ls(e,t)}};wu=function(){return D};ku=function(e,t){var n=D;try{return D=e,t()}finally{D=n}};_i=function(e,t,n){switch(t){case"input":if(ji(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Cl(r);if(!l)throw Error(N(90));qa(r),ji(r,l)}}}break;case"textarea":eu(e,n);break;case"select":t=n.value,t!=null&&tn(e,!!n.multiple,t,!1)}};su=bo;au=$t;var Dp={usingClientEntryPoint:!1,Events:[dr,Yt,Cl,iu,ou,bo]},Ln={findFiberByHostInstance:_t,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Fp={bundleType:Ln.bundleType,version:Ln.version,rendererPackageName:Ln.rendererPackageName,rendererConfig:Ln.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:nt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=du(e),e===null?null:e.stateNode},findFiberByHostInstance:Ln.findFiberByHostInstance||Op,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Or=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Or.isDisabled&&Or.supportsFiber)try{Nl=Or.inject(Fp),Ue=Or}catch{}}Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Dp;Ne.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!os(t))throw Error(N(200));return Mp(e,t,null,n)};Ne.createRoot=function(e,t){if(!os(e))throw Error(N(299));var n=!1,r="",l=Hc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=rs(e,1,!1,null,null,n,!1,r,l),e[qe]=t.current,qn(e.nodeType===8?e.parentNode:e),new is(t)};Ne.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(N(188)):(e=Object.keys(e).join(","),Error(N(268,e)));return e=du(t),e=e===null?null:e.stateNode,e};Ne.flushSync=function(e){return $t(e)};Ne.hydrate=function(e,t,n){if(!Il(t))throw Error(N(200));return Dl(null,e,t,!0,n)};Ne.hydrateRoot=function(e,t,n){if(!os(e))throw Error(N(405));var r=n!=null&&n.hydratedSources||null,l=!1,i="",o=Hc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Vc(t,null,e,1,n??null,l,!1,i,o),e[qe]=t.current,qn(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Ol(t)};Ne.render=function(e,t,n){if(!Il(t))throw Error(N(200));return Dl(null,e,t,!1,n)};Ne.unmountComponentAtNode=function(e){if(!Il(e))throw Error(N(40));return e._reactRootContainer?($t(function(){Dl(null,null,e,!1,function(){e._reactRootContainer=null,e[qe]=null})}),!0):!1};Ne.unstable_batchedUpdates=bo;Ne.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Il(n))throw Error(N(200));if(e==null||e._reactInternals===void 0)throw Error(N(38));return Dl(e,t,n,!1,r)};Ne.version="18.3.1-next-f1338f8080-20240426";function Bc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Bc)}catch(e){console.error(e)}}Bc(),Ba.exports=Ne;var Ap=Ba.exports,Gc,Sa=Ap;Gc=Sa.createRoot,Sa.hydrateRoot;var ss={};Object.defineProperty(ss,"__esModule",{value:!0});ss.parse=Gp;ss.serialize=Qp;const $p=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Up=/^[\u0021-\u003A\u003C-\u007E]*$/,Wp=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Vp=/^[\u0020-\u003A\u003D-\u007E]*$/,Hp=Object.prototype.toString,Bp=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Gp(e,t){const n=new Bp,r=e.length;if(r<2)return n;const l=(t==null?void 0:t.decode)||Kp;let i=0;do{const o=e.indexOf("=",i);if(o===-1)break;const a=e.indexOf(";",i),u=a===-1?r:a;if(o>u){i=e.lastIndexOf(";",o-1)+1;continue}const c=Ea(e,i,o),h=Ca(e,o,c),x=e.slice(c,h);if(n[x]===void 0){let m=Ea(e,o+1,u),w=Ca(e,u,m);const g=l(e.slice(m,w));n[x]=g}i=u+1}while(i<r);return n}function Ea(e,t,n){do{const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}while(++t<n);return n}function Ca(e,t,n){for(;t>n;){const r=e.charCodeAt(--t);if(r!==32&&r!==9)return t+1}return n}function Qp(e,t,n){const r=(n==null?void 0:n.encode)||encodeURIComponent;if(!$p.test(e))throw new TypeError(`argument name is invalid: ${e}`);const l=r(t);if(!Up.test(l))throw new TypeError(`argument val is invalid: ${t}`);let i=e+"="+l;if(!n)return i;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);i+="; Max-Age="+n.maxAge}if(n.domain){if(!Wp.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);i+="; Domain="+n.domain}if(n.path){if(!Vp.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);i+="; Path="+n.path}if(n.expires){if(!Yp(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);i+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(i+="; HttpOnly"),n.secure&&(i+="; Secure"),n.partitioned&&(i+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return i}function Kp(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function Yp(e){return Hp.call(e)==="[object Date]"}var Ra="popstate";function Xp(e={}){function t(r,l){let{pathname:i,search:o,hash:a}=r.location;return uo("",{pathname:i,search:o,hash:a},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function n(r,l){return typeof l=="string"?l:sr(l)}return Jp(t,n,null,e)}function H(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ve(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Zp(){return Math.random().toString(36).substring(2,10)}function Pa(e,t){return{usr:e.state,key:e.key,idx:t}}function uo(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?gn(t):t,state:n,key:t&&t.key||r||Zp()}}function sr({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function gn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function Jp(e,t,n,r={}){let{window:l=document.defaultView,v5Compat:i=!1}=r,o=l.history,a="POP",u=null,c=h();c==null&&(c=0,o.replaceState({...o.state,idx:c},""));function h(){return(o.state||{idx:null}).idx}function x(){a="POP";let E=h(),f=E==null?null:E-c;c=E,u&&u({action:a,location:v.location,delta:f})}function m(E,f){a="PUSH";let d=uo(v.location,E,f);c=h()+1;let p=Pa(d,c),k=v.createHref(d);try{o.pushState(p,"",k)}catch(j){if(j instanceof DOMException&&j.name==="DataCloneError")throw j;l.location.assign(k)}i&&u&&u({action:a,location:v.location,delta:1})}function w(E,f){a="REPLACE";let d=uo(v.location,E,f);c=h();let p=Pa(d,c),k=v.createHref(d);o.replaceState(p,"",k),i&&u&&u({action:a,location:v.location,delta:0})}function g(E){return qp(E)}let v={get action(){return a},get location(){return e(l,o)},listen(E){if(u)throw new Error("A history only accepts one active listener");return l.addEventListener(Ra,x),u=E,()=>{l.removeEventListener(Ra,x),u=null}},createHref(E){return t(l,E)},createURL:g,encodeLocation(E){let f=g(E);return{pathname:f.pathname,search:f.search,hash:f.hash}},push:m,replace:w,go(E){return o.go(E)}};return v}function qp(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),H(n,"No window.location.(origin|href) available to create URL");let r=typeof e=="string"?e:sr(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}function Qc(e,t,n="/"){return bp(e,t,n,!1)}function bp(e,t,n,r){let l=typeof t=="string"?gn(t):t,i=tt(l.pathname||"/",n);if(i==null)return null;let o=Kc(e);e0(o);let a=null;for(let u=0;a==null&&u<o.length;++u){let c=d0(i);a=u0(o[u],c,r)}return a}function Kc(e,t=[],n=[],r=""){let l=(i,o,a)=>{let u={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};u.relativePath.startsWith("/")&&(H(u.relativePath.startsWith(r),`Absolute route path "${u.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),u.relativePath=u.relativePath.slice(r.length));let c=Ze([r,u.relativePath]),h=n.concat(u);i.children&&i.children.length>0&&(H(i.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),Kc(i.children,t,h,c)),!(i.path==null&&!i.index)&&t.push({path:c,score:s0(c,i.index),routesMeta:h})};return e.forEach((i,o)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))l(i,o);else for(let u of Yc(i.path))l(i,o,u)}),t}function Yc(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,l=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return l?[i,""]:[i];let o=Yc(r.join("/")),a=[];return a.push(...o.map(u=>u===""?i:[i,u].join("/"))),l&&a.push(...o),a.map(u=>e.startsWith("/")&&u===""?"/":u)}function e0(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:a0(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var t0=/^:[\w-]+$/,n0=3,r0=2,l0=1,i0=10,o0=-2,La=e=>e==="*";function s0(e,t){let n=e.split("/"),r=n.length;return n.some(La)&&(r+=o0),t&&(r+=r0),n.filter(l=>!La(l)).reduce((l,i)=>l+(t0.test(i)?n0:i===""?l0:i0),r)}function a0(e,t){return e.length===t.length&&e.slice(0,-1).every((r,l)=>r===t[l])?e[e.length-1]-t[t.length-1]:0}function u0(e,t,n=!1){let{routesMeta:r}=e,l={},i="/",o=[];for(let a=0;a<r.length;++a){let u=r[a],c=a===r.length-1,h=i==="/"?t:t.slice(i.length)||"/",x=wl({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},h),m=u.route;if(!x&&c&&n&&!r[r.length-1].route.index&&(x=wl({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},h)),!x)return null;Object.assign(l,x.params),o.push({params:l,pathname:Ze([i,x.pathname]),pathnameBase:h0(Ze([i,x.pathnameBase])),route:m}),x.pathnameBase!=="/"&&(i=Ze([i,x.pathnameBase]))}return o}function wl(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=c0(e.path,e.caseSensitive,e.end),l=t.match(n);if(!l)return null;let i=l[0],o=i.replace(/(.)\/+$/,"$1"),a=l.slice(1);return{params:r.reduce((c,{paramName:h,isOptional:x},m)=>{if(h==="*"){let g=a[m]||"";o=i.slice(0,i.length-g.length).replace(/(.)\/+$/,"$1")}const w=a[m];return x&&!w?c[h]=void 0:c[h]=(w||"").replace(/%2F/g,"/"),c},{}),pathname:i,pathnameBase:o,pattern:e}}function c0(e,t=!1,n=!0){Ve(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,u)=>(r.push({paramName:a,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),r]}function d0(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ve(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function tt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function f0(e,t="/"){let{pathname:n,search:r="",hash:l=""}=typeof e=="string"?gn(e):e;return{pathname:n?n.startsWith("/")?n:p0(n,t):t,search:x0(r),hash:y0(l)}}function p0(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?n.length>1&&n.pop():l!=="."&&n.push(l)}),n.length>1?n.join("/"):"/"}function hi(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function m0(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Xc(e){let t=m0(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function Zc(e,t,n,r=!1){let l;typeof e=="string"?l=gn(e):(l={...e},H(!l.pathname||!l.pathname.includes("?"),hi("?","pathname","search",l)),H(!l.pathname||!l.pathname.includes("#"),hi("#","pathname","hash",l)),H(!l.search||!l.search.includes("#"),hi("#","search","hash",l)));let i=e===""||l.pathname==="",o=i?"/":l.pathname,a;if(o==null)a=n;else{let x=t.length-1;if(!r&&o.startsWith("..")){let m=o.split("/");for(;m[0]==="..";)m.shift(),x-=1;l.pathname=m.join("/")}a=x>=0?t[x]:"/"}let u=f0(l,a),c=o&&o!=="/"&&o.endsWith("/"),h=(i||o===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(c||h)&&(u.pathname+="/"),u}var Ze=e=>e.join("/").replace(/\/\/+/g,"/"),h0=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),x0=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,y0=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function g0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Jc=["POST","PUT","PATCH","DELETE"];new Set(Jc);var v0=["GET",...Jc];new Set(v0);var vn=y.createContext(null);vn.displayName="DataRouter";var Fl=y.createContext(null);Fl.displayName="DataRouterState";var qc=y.createContext({isTransitioning:!1});qc.displayName="ViewTransition";var w0=y.createContext(new Map);w0.displayName="Fetchers";var k0=y.createContext(null);k0.displayName="Await";var He=y.createContext(null);He.displayName="Navigation";var pr=y.createContext(null);pr.displayName="Location";var rt=y.createContext({outlet:null,matches:[],isDataRoute:!1});rt.displayName="Route";var as=y.createContext(null);as.displayName="RouteError";function N0(e,{relative:t}={}){H(mr(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=y.useContext(He),{hash:l,pathname:i,search:o}=hr(e,{relative:t}),a=i;return n!=="/"&&(a=i==="/"?n:Ze([n,i])),r.createHref({pathname:a,search:o,hash:l})}function mr(){return y.useContext(pr)!=null}function Et(){return H(mr(),"useLocation() may be used only in the context of a <Router> component."),y.useContext(pr).location}var bc="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ed(e){y.useContext(He).static||y.useLayoutEffect(e)}function j0(){let{isDataRoute:e}=y.useContext(rt);return e?D0():S0()}function S0(){H(mr(),"useNavigate() may be used only in the context of a <Router> component.");let e=y.useContext(vn),{basename:t,navigator:n}=y.useContext(He),{matches:r}=y.useContext(rt),{pathname:l}=Et(),i=JSON.stringify(Xc(r)),o=y.useRef(!1);return ed(()=>{o.current=!0}),y.useCallback((u,c={})=>{if(Ve(o.current,bc),!o.current)return;if(typeof u=="number"){n.go(u);return}let h=Zc(u,JSON.parse(i),l,c.relative==="path");e==null&&t!=="/"&&(h.pathname=h.pathname==="/"?t:Ze([t,h.pathname])),(c.replace?n.replace:n.push)(h,c.state,c)},[t,n,i,l,e])}y.createContext(null);function hr(e,{relative:t}={}){let{matches:n}=y.useContext(rt),{pathname:r}=Et(),l=JSON.stringify(Xc(n));return y.useMemo(()=>Zc(e,JSON.parse(l),r,t==="path"),[e,l,r,t])}function E0(e,t){return td(e,t)}function td(e,t,n,r){var f;H(mr(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=y.useContext(He),{matches:i}=y.useContext(rt),o=i[i.length-1],a=o?o.params:{},u=o?o.pathname:"/",c=o?o.pathnameBase:"/",h=o&&o.route;{let d=h&&h.path||"";nd(u,!h||d.endsWith("*")||d.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${d}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${d}"> to <Route path="${d==="/"?"*":`${d}/*`}">.`)}let x=Et(),m;if(t){let d=typeof t=="string"?gn(t):t;H(c==="/"||((f=d.pathname)==null?void 0:f.startsWith(c)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${d.pathname}" was given in the \`location\` prop.`),m=d}else m=x;let w=m.pathname||"/",g=w;if(c!=="/"){let d=c.replace(/^\//,"").split("/");g="/"+w.replace(/^\//,"").split("/").slice(d.length).join("/")}let v=Qc(e,{pathname:g});Ve(h||v!=null,`No routes matched location "${m.pathname}${m.search}${m.hash}" `),Ve(v==null||v[v.length-1].route.element!==void 0||v[v.length-1].route.Component!==void 0||v[v.length-1].route.lazy!==void 0,`Matched leaf route at location "${m.pathname}${m.search}${m.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let E=_0(v&&v.map(d=>Object.assign({},d,{params:Object.assign({},a,d.params),pathname:Ze([c,l.encodeLocation?l.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?c:Ze([c,l.encodeLocation?l.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),i,n,r);return t&&E?y.createElement(pr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...m},navigationType:"POP"}},E):E}function C0(){let e=I0(),t=g0(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=y.createElement(y.Fragment,null,y.createElement("p",null,"💿 Hey developer 👋"),y.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",y.createElement("code",{style:i},"ErrorBoundary")," or"," ",y.createElement("code",{style:i},"errorElement")," prop on your route.")),y.createElement(y.Fragment,null,y.createElement("h2",null,"Unexpected Application Error!"),y.createElement("h3",{style:{fontStyle:"italic"}},t),n?y.createElement("pre",{style:l},n):null,o)}var R0=y.createElement(C0,null),P0=class extends y.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?y.createElement(rt.Provider,{value:this.props.routeContext},y.createElement(as.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function L0({routeContext:e,match:t,children:n}){let r=y.useContext(vn);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),y.createElement(rt.Provider,{value:e},n)}function _0(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let l=e,i=n==null?void 0:n.errors;if(i!=null){let u=l.findIndex(c=>c.route.id&&(i==null?void 0:i[c.route.id])!==void 0);H(u>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),l=l.slice(0,Math.min(l.length,u+1))}let o=!1,a=-1;if(n)for(let u=0;u<l.length;u++){let c=l[u];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(a=u),c.route.id){let{loaderData:h,errors:x}=n,m=c.route.loader&&!h.hasOwnProperty(c.route.id)&&(!x||x[c.route.id]===void 0);if(c.route.lazy||m){o=!0,a>=0?l=l.slice(0,a+1):l=[l[0]];break}}}return l.reduceRight((u,c,h)=>{let x,m=!1,w=null,g=null;n&&(x=i&&c.route.id?i[c.route.id]:void 0,w=c.route.errorElement||R0,o&&(a<0&&h===0?(nd("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),m=!0,g=null):a===h&&(m=!0,g=c.route.hydrateFallbackElement||null)));let v=t.concat(l.slice(0,h+1)),E=()=>{let f;return x?f=w:m?f=g:c.route.Component?f=y.createElement(c.route.Component,null):c.route.element?f=c.route.element:f=u,y.createElement(L0,{match:c,routeContext:{outlet:u,matches:v,isDataRoute:n!=null},children:f})};return n&&(c.route.ErrorBoundary||c.route.errorElement||h===0)?y.createElement(P0,{location:n.location,revalidation:n.revalidation,component:w,error:x,children:E(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):E()},null)}function us(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function z0(e){let t=y.useContext(vn);return H(t,us(e)),t}function T0(e){let t=y.useContext(Fl);return H(t,us(e)),t}function M0(e){let t=y.useContext(rt);return H(t,us(e)),t}function cs(e){let t=M0(e),n=t.matches[t.matches.length-1];return H(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function O0(){return cs("useRouteId")}function I0(){var r;let e=y.useContext(as),t=T0("useRouteError"),n=cs("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function D0(){let{router:e}=z0("useNavigate"),t=cs("useNavigate"),n=y.useRef(!1);return ed(()=>{n.current=!0}),y.useCallback(async(l,i={})=>{Ve(n.current,bc),n.current&&(typeof l=="number"?e.navigate(l):await e.navigate(l,{fromRouteId:t,...i}))},[e,t])}var _a={};function nd(e,t,n){!t&&!_a[e]&&(_a[e]=!0,Ve(!1,n))}y.memo(F0);function F0({routes:e,future:t,state:n}){return td(e,void 0,n,t)}function it(e){H(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function A0({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:l,static:i=!1}){H(!mr(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let o=e.replace(/^\/*/,"/"),a=y.useMemo(()=>({basename:o,navigator:l,static:i,future:{}}),[o,l,i]);typeof n=="string"&&(n=gn(n));let{pathname:u="/",search:c="",hash:h="",state:x=null,key:m="default"}=n,w=y.useMemo(()=>{let g=tt(u,o);return g==null?null:{location:{pathname:g,search:c,hash:h,state:x,key:m},navigationType:r}},[o,u,c,h,x,m,r]);return Ve(w!=null,`<Router basename="${o}"> is not able to match the URL "${u}${c}${h}" because it does not start with the basename, so the <Router> won't render anything.`),w==null?null:y.createElement(He.Provider,{value:a},y.createElement(pr.Provider,{children:t,value:w}))}function $0({children:e,location:t}){return E0(co(e),t)}function co(e,t=[]){let n=[];return y.Children.forEach(e,(r,l)=>{if(!y.isValidElement(r))return;let i=[...t,l];if(r.type===y.Fragment){n.push.apply(n,co(r.props.children,i));return}H(r.type===it,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),H(!r.props.index||!r.props.children,"An index route cannot have child routes.");let o={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=co(r.props.children,i)),n.push(o)}),n}var Yr="get",Xr="application/x-www-form-urlencoded";function Al(e){return e!=null&&typeof e.tagName=="string"}function U0(e){return Al(e)&&e.tagName.toLowerCase()==="button"}function W0(e){return Al(e)&&e.tagName.toLowerCase()==="form"}function V0(e){return Al(e)&&e.tagName.toLowerCase()==="input"}function H0(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function B0(e,t){return e.button===0&&(!t||t==="_self")&&!H0(e)}var Ir=null;function G0(){if(Ir===null)try{new FormData(document.createElement("form"),0),Ir=!1}catch{Ir=!0}return Ir}var Q0=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function xi(e){return e!=null&&!Q0.has(e)?(Ve(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Xr}"`),null):e}function K0(e,t){let n,r,l,i,o;if(W0(e)){let a=e.getAttribute("action");r=a?tt(a,t):null,n=e.getAttribute("method")||Yr,l=xi(e.getAttribute("enctype"))||Xr,i=new FormData(e)}else if(U0(e)||V0(e)&&(e.type==="submit"||e.type==="image")){let a=e.form;if(a==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let u=e.getAttribute("formaction")||a.getAttribute("action");if(r=u?tt(u,t):null,n=e.getAttribute("formmethod")||a.getAttribute("method")||Yr,l=xi(e.getAttribute("formenctype"))||xi(a.getAttribute("enctype"))||Xr,i=new FormData(a,e),!G0()){let{name:c,type:h,value:x}=e;if(h==="image"){let m=c?`${c}.`:"";i.append(`${m}x`,"0"),i.append(`${m}y`,"0")}else c&&i.append(c,x)}}else{if(Al(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Yr,r=null,l=Xr,o=e}return i&&l==="text/plain"&&(o=i,i=void 0),{action:r,method:n.toLowerCase(),encType:l,formData:i,body:o}}function ds(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function Y0(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function X0(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Z0(e,t,n){let r=await Promise.all(e.map(async l=>{let i=t.routes[l.route.id];if(i){let o=await Y0(i,n);return o.links?o.links():[]}return[]}));return em(r.flat(1).filter(X0).filter(l=>l.rel==="stylesheet"||l.rel==="preload").map(l=>l.rel==="stylesheet"?{...l,rel:"prefetch",as:"style"}:{...l,rel:"prefetch"}))}function za(e,t,n,r,l,i){let o=(u,c)=>n[c]?u.route.id!==n[c].route.id:!0,a=(u,c)=>{var h;return n[c].pathname!==u.pathname||((h=n[c].route.path)==null?void 0:h.endsWith("*"))&&n[c].params["*"]!==u.params["*"]};return i==="assets"?t.filter((u,c)=>o(u,c)||a(u,c)):i==="data"?t.filter((u,c)=>{var x;let h=r.routes[u.route.id];if(!h||!h.hasLoader)return!1;if(o(u,c)||a(u,c))return!0;if(u.route.shouldRevalidate){let m=u.route.shouldRevalidate({currentUrl:new URL(l.pathname+l.search+l.hash,window.origin),currentParams:((x=n[0])==null?void 0:x.params)||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:!0});if(typeof m=="boolean")return m}return!0}):[]}function J0(e,t,{includeHydrateFallback:n}={}){return q0(e.map(r=>{let l=t.routes[r.route.id];if(!l)return[];let i=[l.module];return l.clientActionModule&&(i=i.concat(l.clientActionModule)),l.clientLoaderModule&&(i=i.concat(l.clientLoaderModule)),n&&l.hydrateFallbackModule&&(i=i.concat(l.hydrateFallbackModule)),l.imports&&(i=i.concat(l.imports)),i}).flat(1))}function q0(e){return[...new Set(e)]}function b0(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function em(e,t){let n=new Set;return new Set(t),e.reduce((r,l)=>{let i=JSON.stringify(b0(l));return n.has(i)||(n.add(i),r.push({key:i,link:l})),r},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var tm=new Set([100,101,204,205]);function nm(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&tt(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function rd(){let e=y.useContext(vn);return ds(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function rm(){let e=y.useContext(Fl);return ds(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var fs=y.createContext(void 0);fs.displayName="FrameworkContext";function ld(){let e=y.useContext(fs);return ds(e,"You must render this element inside a <HydratedRouter> element"),e}function lm(e,t){let n=y.useContext(fs),[r,l]=y.useState(!1),[i,o]=y.useState(!1),{onFocus:a,onBlur:u,onMouseEnter:c,onMouseLeave:h,onTouchStart:x}=t,m=y.useRef(null);y.useEffect(()=>{if(e==="render"&&o(!0),e==="viewport"){let v=f=>{f.forEach(d=>{o(d.isIntersecting)})},E=new IntersectionObserver(v,{threshold:.5});return m.current&&E.observe(m.current),()=>{E.disconnect()}}},[e]),y.useEffect(()=>{if(r){let v=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(v)}}},[r]);let w=()=>{l(!0)},g=()=>{l(!1),o(!1)};return n?e!=="intent"?[i,m,{}]:[i,m,{onFocus:_n(a,w),onBlur:_n(u,g),onMouseEnter:_n(c,w),onMouseLeave:_n(h,g),onTouchStart:_n(x,w)}]:[!1,m,{}]}function _n(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function im({page:e,...t}){let{router:n}=rd(),r=y.useMemo(()=>Qc(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?y.createElement(sm,{page:e,matches:r,...t}):null}function om(e){let{manifest:t,routeModules:n}=ld(),[r,l]=y.useState([]);return y.useEffect(()=>{let i=!1;return Z0(e,t,n).then(o=>{i||l(o)}),()=>{i=!0}},[e,t,n]),r}function sm({page:e,matches:t,...n}){let r=Et(),{manifest:l,routeModules:i}=ld(),{basename:o}=rd(),{loaderData:a,matches:u}=rm(),c=y.useMemo(()=>za(e,t,u,l,r,"data"),[e,t,u,l,r]),h=y.useMemo(()=>za(e,t,u,l,r,"assets"),[e,t,u,l,r]),x=y.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let g=new Set,v=!1;if(t.forEach(f=>{var p;let d=l.routes[f.route.id];!d||!d.hasLoader||(!c.some(k=>k.route.id===f.route.id)&&f.route.id in a&&((p=i[f.route.id])!=null&&p.shouldRevalidate)||d.hasClientLoader?v=!0:g.add(f.route.id))}),g.size===0)return[];let E=nm(e,o);return v&&g.size>0&&E.searchParams.set("_routes",t.filter(f=>g.has(f.route.id)).map(f=>f.route.id).join(",")),[E.pathname+E.search]},[o,a,r,l,c,t,e,i]),m=y.useMemo(()=>J0(h,l),[h,l]),w=om(h);return y.createElement(y.Fragment,null,x.map(g=>y.createElement("link",{key:g,rel:"prefetch",as:"fetch",href:g,...n})),m.map(g=>y.createElement("link",{key:g,rel:"modulepreload",href:g,...n})),w.map(({key:g,link:v})=>y.createElement("link",{key:g,...v})))}function am(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var id=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{id&&(window.__reactRouterVersion="7.6.3")}catch{}function um({basename:e,children:t,window:n}){let r=y.useRef();r.current==null&&(r.current=Xp({window:n,v5Compat:!0}));let l=r.current,[i,o]=y.useState({action:l.action,location:l.location}),a=y.useCallback(u=>{y.startTransition(()=>o(u))},[o]);return y.useLayoutEffect(()=>l.listen(a),[l,a]),y.createElement(A0,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:l})}var od=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Qe=y.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:l,reloadDocument:i,replace:o,state:a,target:u,to:c,preventScrollReset:h,viewTransition:x,...m},w){let{basename:g}=y.useContext(He),v=typeof c=="string"&&od.test(c),E,f=!1;if(typeof c=="string"&&v&&(E=c,id))try{let O=new URL(window.location.href),_=c.startsWith("//")?new URL(O.protocol+c):new URL(c),fe=tt(_.pathname,g);_.origin===O.origin&&fe!=null?c=fe+_.search+_.hash:f=!0}catch{Ve(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let d=N0(c,{relative:l}),[p,k,j]=lm(r,m),P=pm(c,{replace:o,state:a,target:u,preventScrollReset:h,relative:l,viewTransition:x});function R(O){t&&t(O),O.defaultPrevented||P(O)}let L=y.createElement("a",{...m,...j,href:E||d,onClick:f||i?t:R,ref:am(w,k),target:u,"data-discover":!v&&n==="render"?"true":void 0});return p&&!v?y.createElement(y.Fragment,null,L,y.createElement(im,{page:d})):L});Qe.displayName="Link";var cm=y.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:l=!1,style:i,to:o,viewTransition:a,children:u,...c},h){let x=hr(o,{relative:c.relative}),m=Et(),w=y.useContext(Fl),{navigator:g,basename:v}=y.useContext(He),E=w!=null&&gm(x)&&a===!0,f=g.encodeLocation?g.encodeLocation(x).pathname:x.pathname,d=m.pathname,p=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;n||(d=d.toLowerCase(),p=p?p.toLowerCase():null,f=f.toLowerCase()),p&&v&&(p=tt(p,v)||p);const k=f!=="/"&&f.endsWith("/")?f.length-1:f.length;let j=d===f||!l&&d.startsWith(f)&&d.charAt(k)==="/",P=p!=null&&(p===f||!l&&p.startsWith(f)&&p.charAt(f.length)==="/"),R={isActive:j,isPending:P,isTransitioning:E},L=j?t:void 0,O;typeof r=="function"?O=r(R):O=[r,j?"active":null,P?"pending":null,E?"transitioning":null].filter(Boolean).join(" ");let _=typeof i=="function"?i(R):i;return y.createElement(Qe,{...c,"aria-current":L,className:O,ref:h,style:_,to:o,viewTransition:a},typeof u=="function"?u(R):u)});cm.displayName="NavLink";var dm=y.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:l,state:i,method:o=Yr,action:a,onSubmit:u,relative:c,preventScrollReset:h,viewTransition:x,...m},w)=>{let g=xm(),v=ym(a,{relative:c}),E=o.toLowerCase()==="get"?"get":"post",f=typeof a=="string"&&od.test(a),d=p=>{if(u&&u(p),p.defaultPrevented)return;p.preventDefault();let k=p.nativeEvent.submitter,j=(k==null?void 0:k.getAttribute("formmethod"))||o;g(k||p.currentTarget,{fetcherKey:t,method:j,navigate:n,replace:l,state:i,relative:c,preventScrollReset:h,viewTransition:x})};return y.createElement("form",{ref:w,method:E,action:v,onSubmit:r?u:d,...m,"data-discover":!f&&e==="render"?"true":void 0})});dm.displayName="Form";function fm(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function sd(e){let t=y.useContext(vn);return H(t,fm(e)),t}function pm(e,{target:t,replace:n,state:r,preventScrollReset:l,relative:i,viewTransition:o}={}){let a=j0(),u=Et(),c=hr(e,{relative:i});return y.useCallback(h=>{if(B0(h,t)){h.preventDefault();let x=n!==void 0?n:sr(u)===sr(c);a(e,{replace:x,state:r,preventScrollReset:l,relative:i,viewTransition:o})}},[u,a,c,n,r,t,e,l,i,o])}var mm=0,hm=()=>`__${String(++mm)}__`;function xm(){let{router:e}=sd("useSubmit"),{basename:t}=y.useContext(He),n=O0();return y.useCallback(async(r,l={})=>{let{action:i,method:o,encType:a,formData:u,body:c}=K0(r,t);if(l.navigate===!1){let h=l.fetcherKey||hm();await e.fetch(h,n,l.action||i,{preventScrollReset:l.preventScrollReset,formData:u,body:c,formMethod:l.method||o,formEncType:l.encType||a,flushSync:l.flushSync})}else await e.navigate(l.action||i,{preventScrollReset:l.preventScrollReset,formData:u,body:c,formMethod:l.method||o,formEncType:l.encType||a,replace:l.replace,state:l.state,fromRouteId:n,flushSync:l.flushSync,viewTransition:l.viewTransition})},[e,t,n])}function ym(e,{relative:t}={}){let{basename:n}=y.useContext(He),r=y.useContext(rt);H(r,"useFormAction must be used inside a RouteContext");let[l]=r.matches.slice(-1),i={...hr(e||".",{relative:t})},o=Et();if(e==null){i.search=o.search;let a=new URLSearchParams(i.search),u=a.getAll("index");if(u.some(h=>h==="")){a.delete("index"),u.filter(x=>x).forEach(x=>a.append("index",x));let h=a.toString();i.search=h?`?${h}`:""}}return(!e||e===".")&&l.route.index&&(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(i.pathname=i.pathname==="/"?n:Ze([n,i.pathname])),sr(i)}function gm(e,t={}){let n=y.useContext(qc);H(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=sd("useViewTransitionState"),l=hr(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=tt(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=tt(n.nextLocation.pathname,r)||n.nextLocation.pathname;return wl(l.pathname,o)!=null||wl(l.pathname,i)!=null}[...tm];/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var vm={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wm=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),re=(e,t)=>{const n=y.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:a="",children:u,...c},h)=>y.createElement("svg",{ref:h,...vm,width:l,height:l,stroke:r,strokeWidth:o?Number(i)*24/Number(l):i,className:["lucide",`lucide-${wm(e)}`,a].join(" "),...c},[...t.map(([x,m])=>y.createElement(x,m)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ps=re("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const km=re("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ta=re("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ad=re("Atom",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["path",{d:"M20.2 20.2c2.04-2.03.02-7.36-4.5-11.9-4.54-4.52-9.87-6.54-11.9-4.5-2.04 2.03-.02 7.36 4.5 11.9 4.54 4.52 9.87 6.54 11.9 4.5Z",key:"1l2ple"}],["path",{d:"M15.7 15.7c4.52-4.54 6.54-9.87 4.5-11.9-2.03-2.04-7.36-.02-11.9 4.5-4.52 4.54-6.54 9.87-4.5 11.9 2.03 2.04 7.36.02 11.9-4.5Z",key:"1wam0m"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nm=re("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jm=re("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ud=re("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ms=re("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sm=re("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Em=re("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fo=re("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cm=re("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rm=re("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cd=re("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pm=re("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lm=re("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=re("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),_m=()=>{const[e,t]=y.useState(!1),n=Et(),r=[{id:"ecg",name:"ECG",path:"/ecg",description:"Electrocardiogram"},{id:"emg",name:"EMG",path:"/emg",description:"Electromyogram"},{id:"eeg",name:"EEG",path:"/eeg",description:"Electroencephalogram"},{id:"eog",name:"EOG",path:"/eog",description:"Electrooculogram"},{id:"eng",name:"ENG",path:"/eng",description:"Electroneurogram"},{id:"egg",name:"EGG",path:"/egg",description:"Electrogastrogram"}],l=i=>n.pathname===i;return s.jsx("header",{className:"bg-white shadow-lg border-b-2 border-blue-100",children:s.jsxs("div",{className:"container mx-auto px-4",children:[s.jsxs("div",{className:"flex justify-between items-center py-4",children:[s.jsxs(Qe,{to:"/",className:"flex items-center space-x-3 group",children:[s.jsxs("div",{className:"relative",children:[s.jsx(ps,{className:"h-8 w-8 text-blue-600 transition-transform group-hover:scale-110"}),s.jsx($l,{className:"h-4 w-4 text-teal-500 absolute -top-1 -right-1"})]}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"BioMed Lab"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Circuit Documentation"})]})]}),s.jsxs("nav",{className:"hidden lg:flex space-x-1",children:[s.jsx(Qe,{to:"/",className:`px-4 py-2 rounded-lg font-medium transition-colors ${l("/")?"bg-blue-600 text-white":"text-gray-700 hover:bg-blue-50"}`,children:"Home"}),r.map(i=>s.jsx(Qe,{to:i.path,className:`px-4 py-2 rounded-lg font-medium transition-colors ${l(i.path)?"bg-blue-600 text-white":"text-gray-700 hover:bg-blue-50"}`,title:i.description,children:i.name},i.id))]}),s.jsx("button",{className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>t(!e),children:e?s.jsx(Lm,{className:"h-6 w-6"}):s.jsx(Rm,{className:"h-6 w-6"})})]}),e&&s.jsx("div",{className:"lg:hidden py-4 border-t border-gray-200",children:s.jsxs("div",{className:"space-y-2",children:[s.jsx(Qe,{to:"/",className:`block px-4 py-3 rounded-lg font-medium transition-colors ${l("/")?"bg-blue-600 text-white":"text-gray-700 hover:bg-blue-50"}`,onClick:()=>t(!1),children:"Home"}),r.map(i=>s.jsx(Qe,{to:i.path,className:`block px-4 py-3 rounded-lg font-medium transition-colors ${l(i.path)?"bg-blue-600 text-white":"text-gray-700 hover:bg-blue-50"}`,onClick:()=>t(!1),children:s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold",children:i.name}),s.jsx("span",{className:"block text-sm text-gray-500",children:i.description})]})},i.id))]})})]})})},zm=()=>{const e=[{id:"ecg",name:"ECG",fullName:"Electrocardiogram",description:"Record electrical activity of the heart with Lead I configuration",icon:fo,color:"bg-red-500",path:"/ecg",frequency:"0.5-40 Hz",amplitude:"0.1-3 mV"},{id:"emg",name:"EMG",fullName:"Electromyogram",description:"Surface recording of muscle electrical activity during contraction",icon:$l,color:"bg-orange-500",path:"/emg",frequency:"10-500 Hz",amplitude:"0.1-5 mV"},{id:"eeg",name:"EEG",fullName:"Electroencephalogram",description:"Single-channel brain activity recording with alpha rhythm detection",icon:ud,color:"bg-purple-500",path:"/eeg",frequency:"0.5-70 Hz",amplitude:"10-100 μV"},{id:"eog",name:"EOG",fullName:"Electrooculogram",description:"Dual-channel eye movement tracking (horizontal and vertical)",icon:ms,color:"bg-blue-500",path:"/eog",frequency:"0.01-30 Hz",amplitude:"10-200 μV"},{id:"eng",name:"ENG",fullName:"Electroneurogram",description:"Nerve conduction study with SNAP recording and velocity calculation",icon:ps,color:"bg-green-500",path:"/eng",frequency:"1-3000 Hz",amplitude:"1-50 μV"},{id:"egg",name:"EGG",fullName:"Electrogastrogram",description:"Gastric slow wave recording with respiratory artifact filtering",icon:ad,color:"bg-teal-500",path:"/egg",frequency:"0.01-0.5 Hz",amplitude:"100-1000 μV"}],t=[{icon:Pm,title:"Safety First",description:"All circuits designed with patient safety and isolation in mind"},{icon:Nm,title:"Complete Documentation",description:"Detailed schematics, component lists, and measurement procedures"},{icon:cd,title:"Production Ready",description:"Professional-grade circuits suitable for educational and research use"}];return s.jsxs("div",{className:"space-y-16",children:[s.jsx("section",{className:"text-center py-16 bg-gradient-to-r from-blue-600 to-teal-600 text-white rounded-3xl shadow-2xl",children:s.jsxs("div",{className:"max-w-4xl mx-auto px-8",children:[s.jsxs("h1",{className:"text-5xl font-bold mb-6",children:["Biomedical Signal Recording",s.jsx("span",{className:"block text-3xl font-normal mt-2 text-blue-100",children:"Circuit Documentation"})]}),s.jsx("p",{className:"text-xl mb-8 text-blue-100 leading-relaxed",children:"Complete documentation for six essential biomedical experiments including detailed circuit schematics, component specifications, electrode placement guides, and expected waveforms."}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsxs(Qe,{to:"/ecg",className:"inline-flex items-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors shadow-lg",children:["Start with ECG",s.jsx(Ta,{className:"ml-2 h-5 w-5"})]}),s.jsx("a",{href:"#experiments",className:"inline-flex items-center px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors",children:"View All Experiments"})]})]})}),s.jsxs("section",{className:"py-16",children:[s.jsxs("div",{className:"text-center mb-12",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Why Choose Our Documentation?"}),s.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Professional-grade documentation with attention to detail and educational value"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:t.map((n,r)=>s.jsxs("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow",children:[s.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-xl mb-4",children:s.jsx(n.icon,{className:"h-8 w-8 text-blue-600"})}),s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:n.title}),s.jsx("p",{className:"text-gray-600",children:n.description})]},r))})]}),s.jsxs("section",{id:"experiments",className:"py-16",children:[s.jsxs("div",{className:"text-center mb-12",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Available Experiments"}),s.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Each experiment includes complete circuit schematics, component specifications, and measurement procedures"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(n=>s.jsx(Qe,{to:n.path,className:"group bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden",children:s.jsxs("div",{className:"p-6",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("div",{className:`p-3 rounded-lg ${n.color} text-white`,children:s.jsx(n.icon,{className:"h-6 w-6"})}),s.jsx(Ta,{className:"h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors"})]}),s.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:[n.name," - ",n.fullName]}),s.jsx("p",{className:"text-gray-600 mb-4",children:n.description}),s.jsxs("div",{className:"space-y-2 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-500",children:"Frequency Range:"}),s.jsx("span",{className:"font-medium",children:n.frequency})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-500",children:"Amplitude Range:"}),s.jsx("span",{className:"font-medium",children:n.amplitude})]})]})]})},n.id))})]}),s.jsx("section",{className:"py-16 bg-gray-50 rounded-3xl",children:s.jsxs("div",{className:"max-w-4xl mx-auto px-8",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-8 text-center",children:"Technical Overview"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[s.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-lg",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Circuit Design"}),s.jsxs("ul",{className:"space-y-2 text-gray-600",children:[s.jsx("li",{children:"• AD623 instrumentation amplifier for high CMRR"}),s.jsx("li",{children:"• MCP6002 dual op-amp for buffering and filtering"}),s.jsx("li",{children:"• Single supply operation (5V or 3.3V)"}),s.jsx("li",{children:"• Proper grounding and shielding techniques"}),s.jsx("li",{children:"• Patient safety isolation considerations"})]})]}),s.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-lg",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Documentation Includes"}),s.jsxs("ul",{className:"space-y-2 text-gray-600",children:[s.jsx("li",{children:"• Complete circuit schematics with pin assignments"}),s.jsx("li",{children:"• Component tables with part numbers and specifications"}),s.jsx("li",{children:"• Electrode placement diagrams"}),s.jsx("li",{children:"• Expected waveforms and measurement procedures"}),s.jsx("li",{children:"• Troubleshooting guides and safety notes"})]})]})]})]})})]})},Tm=({type:e,title:t,description:n})=>{const r=()=>s.jsxs("svg",{viewBox:"0 0 800 600",className:"w-full h-auto border border-gray-300 rounded-lg bg-white",children:[s.jsx("text",{x:"400",y:"30",textAnchor:"middle",className:"fill-gray-700 text-lg font-semibold",children:t}),s.jsx("rect",{x:"50",y:"60",width:"60",height:"40",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"80",y:"85",textAnchor:"middle",className:"fill-gray-700 text-sm",children:"+5V"}),s.jsx("line",{x1:"50",y1:"120",x2:"110",y2:"120",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"70",y1:"120",x2:"70",y2:"130",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"80",y1:"120",x2:"80",y2:"130",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"90",y1:"120",x2:"90",y2:"130",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"80",y:"145",textAnchor:"middle",className:"fill-gray-700 text-sm",children:"GND"}),s.jsx("rect",{x:"150",y:"70",width:"20",height:"60",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"180",y:"85",className:"fill-gray-700 text-xs",children:"R1"}),s.jsx("text",{x:"180",y:"100",className:"fill-gray-700 text-xs",children:"10kΩ"}),s.jsx("rect",{x:"150",y:"140",width:"20",height:"60",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"180",y:"155",className:"fill-gray-700 text-xs",children:"R2"}),s.jsx("text",{x:"180",y:"170",className:"fill-gray-700 text-xs",children:"10kΩ"}),s.jsx("polygon",{points:"220,130 220,170 270,150",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"240",y:"145",textAnchor:"middle",className:"fill-gray-700 text-xs",children:"U2A"}),s.jsx("text",{x:"240",y:"190",textAnchor:"middle",className:"fill-gray-700 text-xs",children:"MCP6002"}),s.jsx("circle",{cx:"300",cy:"150",r:"3",fill:"#333"}),s.jsx("text",{x:"310",y:"155",className:"fill-gray-700 text-sm",children:"Vref"}),s.jsx("circle",{cx:"100",cy:"250",r:"8",fill:"#e74c3c"}),s.jsx("text",{x:"110",y:"255",className:"fill-gray-700 text-sm",children:"LA"}),s.jsx("circle",{cx:"100",cy:"300",r:"8",fill:"#3498db"}),s.jsx("text",{x:"110",y:"305",className:"fill-gray-700 text-sm",children:"RA"}),s.jsx("circle",{cx:"100",cy:"350",r:"8",fill:"#2ecc71"}),s.jsx("text",{x:"110",y:"355",className:"fill-gray-700 text-sm",children:"RL"}),s.jsx("rect",{x:"200",y:"270",width:"120",height:"80",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"260",y:"290",textAnchor:"middle",className:"fill-gray-700 text-sm font-semibold",children:"AD623"}),s.jsx("text",{x:"260",y:"305",textAnchor:"middle",className:"fill-gray-700 text-xs",children:"Instrumentation"}),s.jsx("text",{x:"260",y:"318",textAnchor:"middle",className:"fill-gray-700 text-xs",children:"Amplifier"}),s.jsx("text",{x:"190",y:"290",className:"fill-gray-700 text-xs",children:"+IN"}),s.jsx("text",{x:"190",y:"305",className:"fill-gray-700 text-xs",children:"-IN"}),s.jsx("text",{x:"190",y:"320",className:"fill-gray-700 text-xs",children:"REF"}),s.jsx("text",{x:"330",y:"290",className:"fill-gray-700 text-xs",children:"OUT"}),s.jsx("text",{x:"330",y:"305",className:"fill-gray-700 text-xs",children:"+Vs"}),s.jsx("text",{x:"330",y:"320",className:"fill-gray-700 text-xs",children:"-Vs"}),s.jsx("rect",{x:"235",y:"330",width:"50",height:"15",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"260",y:"340",textAnchor:"middle",className:"fill-gray-700 text-xs",children:"Rg = 100Ω"}),s.jsx("rect",{x:"400",y:"280",width:"20",height:"40",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"390",y:"295",className:"fill-gray-700 text-xs",children:"C_hpf"}),s.jsx("text",{x:"390",y:"308",className:"fill-gray-700 text-xs",children:"0.33μF"}),s.jsx("rect",{x:"450",y:"285",width:"20",height:"30",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"480",y:"295",className:"fill-gray-700 text-xs",children:"R_hpf"}),s.jsx("text",{x:"480",y:"308",className:"fill-gray-700 text-xs",children:"1MΩ"}),s.jsx("rect",{x:"520",y:"285",width:"20",height:"30",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"550",y:"295",className:"fill-gray-700 text-xs",children:"R_lpf"}),s.jsx("text",{x:"550",y:"308",className:"fill-gray-700 text-xs",children:"4kΩ"}),s.jsx("rect",{x:"570",y:"280",width:"20",height:"40",fill:"none",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"600",y:"295",className:"fill-gray-700 text-xs",children:"C_lpf"}),s.jsx("text",{x:"600",y:"308",className:"fill-gray-700 text-xs",children:"1μF"}),s.jsx("circle",{cx:"650",cy:"300",r:"5",fill:"#f39c12"}),s.jsx("text",{x:"660",y:"305",className:"fill-gray-700 text-sm",children:"ECG Out"}),s.jsx("line",{x1:"110",y1:"80",x2:"150",y2:"80",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"160",y1:"100",x2:"160",y2:"130",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"160",y1:"130",x2:"160",y2:"140",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"160",y1:"140",x2:"160",y2:"200",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"80",y1:"120",x2:"160",y2:"120",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"160",y1:"200",x2:"160",y2:"200",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"170",y1:"130",x2:"220",y2:"130",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"270",y1:"150",x2:"300",y2:"150",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"108",y1:"250",x2:"200",y2:"250",stroke:"#e74c3c",strokeWidth:"2"}),s.jsx("line",{x1:"200",y1:"250",x2:"200",y2:"285",stroke:"#e74c3c",strokeWidth:"2"}),s.jsx("line",{x1:"108",y1:"300",x2:"200",y2:"300",stroke:"#3498db",strokeWidth:"2"}),s.jsx("line",{x1:"108",y1:"350",x2:"200",y2:"350",stroke:"#2ecc71",strokeWidth:"2"}),s.jsx("line",{x1:"200",y1:"350",x2:"200",y2:"315",stroke:"#2ecc71",strokeWidth:"2"}),s.jsx("line",{x1:"320",y1:"285",x2:"400",y2:"285",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"420",y1:"300",x2:"450",y2:"300",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"470",y1:"300",x2:"520",y2:"300",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"540",y1:"300",x2:"570",y2:"300",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"590",y1:"300",x2:"650",y2:"300",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"430",y:"360",className:"fill-gray-600 text-xs",children:"HPF: 0.5Hz"}),s.jsx("text",{x:"550",y:"360",className:"fill-gray-600 text-xs",children:"LPF: 40Hz"}),s.jsx("text",{x:"260",y:"380",className:"fill-gray-600 text-xs",children:"Gain: 500"})]});return s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{className:"bg-gray-50 p-4 rounded-lg",children:s.jsx("p",{className:"text-gray-700",children:n})}),s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:e==="ecg"&&r()}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[s.jsxs("div",{className:"bg-red-50 p-3 rounded-lg",children:[s.jsx("div",{className:"w-4 h-4 bg-red-500 rounded-full mb-2"}),s.jsx("span",{className:"text-red-700 font-medium",children:"Positive Input (LA)"})]}),s.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg",children:[s.jsx("div",{className:"w-4 h-4 bg-blue-500 rounded-full mb-2"}),s.jsx("span",{className:"text-blue-700 font-medium",children:"Negative Input (RA)"})]}),s.jsxs("div",{className:"bg-green-50 p-3 rounded-lg",children:[s.jsx("div",{className:"w-4 h-4 bg-green-500 rounded-full mb-2"}),s.jsx("span",{className:"text-green-700 font-medium",children:"Reference (RL)"})]}),s.jsxs("div",{className:"bg-orange-50 p-3 rounded-lg",children:[s.jsx("div",{className:"w-4 h-4 bg-orange-500 rounded-full mb-2"}),s.jsx("span",{className:"text-orange-700 font-medium",children:"Signal Output"})]})]})]})},Mm=({components:e})=>s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full table-auto",children:[s.jsx("thead",{children:s.jsxs("tr",{className:"bg-gray-50",children:[s.jsx("th",{className:"px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Reference"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Component Type"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Value/Part Number"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Specifications"})]})}),s.jsx("tbody",{className:"divide-y divide-gray-200",children:e.map((t,n)=>s.jsxs("tr",{className:"hover:bg-gray-50",children:[s.jsx("td",{className:"px-4 py-3 text-sm font-medium text-gray-900",children:t.reference}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:t.type}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600 font-mono",children:t.value}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:t.specs})]},n))})]})}),Om=({type:e,title:t,description:n})=>{const r=()=>s.jsxs("svg",{viewBox:"0 0 800 400",className:"w-full h-auto border border-gray-300 rounded-lg bg-white",children:[s.jsx("text",{x:"400",y:"30",textAnchor:"middle",className:"fill-gray-700 text-lg font-semibold",children:t}),s.jsx("defs",{children:s.jsx("pattern",{id:"grid",width:"20",height:"20",patternUnits:"userSpaceOnUse",children:s.jsx("path",{d:"M 20 0 L 0 0 0 20",fill:"none",stroke:"#f0f0f0",strokeWidth:"1"})})}),s.jsx("rect",{x:"50",y:"50",width:"700",height:"300",fill:"url(#grid)"}),s.jsx("line",{x1:"50",y1:"200",x2:"750",y2:"200",stroke:"#333",strokeWidth:"2"}),s.jsx("line",{x1:"50",y1:"50",x2:"50",y2:"350",stroke:"#333",strokeWidth:"2"}),s.jsx("text",{x:"50",y:"370",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"0"}),s.jsx("text",{x:"150",y:"370",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"0.2s"}),s.jsx("text",{x:"250",y:"370",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"0.4s"}),s.jsx("text",{x:"350",y:"370",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"0.6s"}),s.jsx("text",{x:"450",y:"370",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"0.8s"}),s.jsx("text",{x:"550",y:"370",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"1.0s"}),s.jsx("text",{x:"650",y:"370",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"1.2s"}),s.jsx("text",{x:"750",y:"370",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"1.4s"}),s.jsx("text",{x:"40",y:"110",textAnchor:"end",className:"fill-gray-600 text-xs",children:"1mV"}),s.jsx("text",{x:"40",y:"155",textAnchor:"end",className:"fill-gray-600 text-xs",children:"0.5mV"}),s.jsx("text",{x:"40",y:"205",textAnchor:"end",className:"fill-gray-600 text-xs",children:"0"}),s.jsx("text",{x:"40",y:"250",textAnchor:"end",className:"fill-gray-600 text-xs",children:"-0.5mV"}),s.jsx("text",{x:"40",y:"295",textAnchor:"end",className:"fill-gray-600 text-xs",children:"-1mV"}),s.jsx("path",{d:"M 50 200 L 80 200 L 90 180 L 100 200 L 110 200 L 120 220 L 130 120 L 140 280 L 150 200 L 160 200 L 170 170 L 180 200 L 200 200",fill:"none",stroke:"#e74c3c",strokeWidth:"3"}),s.jsx("path",{d:"M 200 200 L 280 200 L 290 180 L 300 200 L 310 200 L 320 220 L 330 120 L 340 280 L 350 200 L 360 200 L 370 170 L 380 200 L 400 200",fill:"none",stroke:"#e74c3c",strokeWidth:"3"}),s.jsx("path",{d:"M 400 200 L 480 200 L 490 180 L 500 200 L 510 200 L 520 220 L 530 120 L 540 280 L 550 200 L 560 200 L 570 170 L 580 200 L 600 200",fill:"none",stroke:"#e74c3c",strokeWidth:"3"}),s.jsx("path",{d:"M 600 200 L 680 200 L 690 180 L 700 200 L 710 200 L 720 220 L 730 120 L 740 280 L 750 200",fill:"none",stroke:"#e74c3c",strokeWidth:"3"}),s.jsx("text",{x:"95",y:"140",className:"fill-blue-600 text-sm font-medium",children:"P"}),s.jsx("text",{x:"135",y:"90",className:"fill-red-600 text-sm font-medium",children:"QRS"}),s.jsx("text",{x:"175",y:"140",className:"fill-green-600 text-sm font-medium",children:"T"}),s.jsx("line",{x1:"90",y1:"330",x2:"130",y2:"330",stroke:"#666",strokeWidth:"1",strokeDasharray:"3,3"}),s.jsx("text",{x:"110",y:"345",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"PR"}),s.jsx("line",{x1:"130",y1:"320",x2:"180",y2:"320",stroke:"#666",strokeWidth:"1",strokeDasharray:"3,3"}),s.jsx("text",{x:"155",y:"335",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"QT"}),s.jsx("line",{x1:"90",y1:"310",x2:"290",y2:"310",stroke:"#666",strokeWidth:"1",strokeDasharray:"3,3"}),s.jsx("text",{x:"190",y:"325",textAnchor:"middle",className:"fill-gray-600 text-xs",children:"R-R Interval"}),s.jsx("text",{x:"600",y:"100",className:"fill-gray-700 text-sm",children:"Heart Rate: 72 bpm"}),s.jsx("text",{x:"600",y:"120",className:"fill-gray-700 text-sm",children:"R-R Interval: 833 ms"})]});return s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{className:"bg-gray-50 p-4 rounded-lg",children:s.jsx("p",{className:"text-gray-700",children:n})}),s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:e==="ecg"&&r()}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[s.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg",children:[s.jsx("span",{className:"text-blue-700 font-medium",children:"P Wave"}),s.jsx("p",{className:"text-blue-600 text-xs mt-1",children:"Atrial depolarization"})]}),s.jsxs("div",{className:"bg-red-50 p-3 rounded-lg",children:[s.jsx("span",{className:"text-red-700 font-medium",children:"QRS Complex"}),s.jsx("p",{className:"text-red-600 text-xs mt-1",children:"Ventricular depolarization"})]}),s.jsxs("div",{className:"bg-green-50 p-3 rounded-lg",children:[s.jsx("span",{className:"text-green-700 font-medium",children:"T Wave"}),s.jsx("p",{className:"text-green-600 text-xs mt-1",children:"Ventricular repolarization"})]})]})]})},Im=()=>{const[e,t]=y.useState("overview"),n=[{id:"overview",label:"Overview",icon:ms},{id:"circuit",label:"Circuit Design",icon:$l},{id:"components",label:"Components",icon:cd},{id:"waveforms",label:"Waveforms",icon:fo},{id:"results",label:"Results",icon:Sm}],r=[{reference:"U1",type:"Instrumentation Amplifier",value:"AD623AN",specs:"DIP-8, Low Noise, High CMRR"},{reference:"U2A",type:"Op-Amp",value:"MCP6002",specs:"Rail-to-Rail, Low Power"},{reference:"Rg",type:"Resistor",value:"100Ω",specs:"1%, Metal Film"},{reference:"R1, R2",type:"Resistor",value:"10kΩ",specs:"1%, Voltage Divider"},{reference:"R_hpf",type:"Resistor",value:"1MΩ",specs:"1%, High Pass Filter"},{reference:"R_lpf",type:"Resistor",value:"4kΩ",specs:"1%, Low Pass Filter"},{reference:"C_hpf",type:"Capacitor",value:"0.33μF",specs:"Low Leakage, Polyester"},{reference:"C_lpf",type:"Capacitor",value:"1μF",specs:"Low Leakage, Ceramic"},{reference:"C_bias",type:"Capacitor",value:"10μF",specs:"Tantalum, 16V"}],l=[{parameter:"Frequency Range",value:"0.5 - 40 Hz",unit:"Hz"},{parameter:"Amplitude Range",value:"0.1 - 3",unit:"mV"},{parameter:"Gain (Rg = 100Ω)",value:"1000",unit:"V/V"},{parameter:"Input Impedance",value:"> 10",unit:"GΩ"},{parameter:"CMRR",value:"> 100",unit:"dB"},{parameter:"Supply Voltage",value:"5",unit:"V"}];return s.jsxs("div",{className:"max-w-7xl mx-auto",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 mb-8",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[s.jsx("div",{className:"p-3 bg-red-100 rounded-xl",children:s.jsx(fo,{className:"h-8 w-8 text-red-600"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"ECG - Electrocardiogram"}),s.jsx("p",{className:"text-lg text-gray-600",children:"Heart electrical activity recording circuit"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"bg-red-50 p-4 rounded-lg",children:[s.jsx("h3",{className:"font-semibold text-red-800 mb-2",children:"Signal Characteristics"}),s.jsx("p",{className:"text-sm text-red-700",children:"P-QRS-T complex, 60-100 bpm"})]}),s.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[s.jsx("h3",{className:"font-semibold text-blue-800 mb-2",children:"Lead Configuration"}),s.jsx("p",{className:"text-sm text-blue-700",children:"Lead I: LA(+), RA(-), RL(ref)"})]}),s.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[s.jsx("h3",{className:"font-semibold text-green-800 mb-2",children:"Applications"}),s.jsx("p",{className:"text-sm text-green-700",children:"Cardiac monitoring, arrhythmia detection"})]})]})]}),s.jsx("div",{className:"bg-white rounded-xl shadow-lg mb-8",children:s.jsx("div",{className:"flex flex-wrap border-b border-gray-200",children:n.map(i=>s.jsxs("button",{onClick:()=>t(i.id),className:`flex items-center space-x-2 px-6 py-4 font-medium transition-colors ${e===i.id?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"}`,children:[s.jsx(i.icon,{className:"h-5 w-5"}),s.jsx("span",{children:i.label})]},i.id))})}),s.jsxs("div",{className:"space-y-8",children:[e==="overview"&&s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Experiment Overview"}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("p",{className:"text-gray-600",children:"This experiment demonstrates the design and implementation of a single-channel ECG recording system using Lead I configuration. The circuit amplifies the small electrical signals generated by the heart and filters them to produce a clean ECG waveform."}),s.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[s.jsx(km,{className:"h-5 w-5 text-yellow-600"}),s.jsx("span",{className:"font-semibold text-yellow-800",children:"Safety Notice"})]}),s.jsx("p",{className:"text-sm text-yellow-700",children:"This circuit is for educational purposes only. Proper isolation and safety measures must be implemented for any human subject testing."})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Technical Specifications"}),s.jsx("div",{className:"space-y-3",children:l.map((i,o)=>s.jsxs("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[s.jsx("span",{className:"text-gray-600",children:i.parameter}),s.jsxs("span",{className:"font-semibold text-gray-900",children:[i.value," ",i.unit]})]},o))})]})]}),e==="circuit"&&s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Complete Circuit Schematic"}),s.jsx(Tm,{type:"ecg",title:"ECG Lead I Recording Circuit",description:"Complete circuit showing instrumentation amplifier, filters, and reference voltage"})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Circuit Stages"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"border-l-4 border-blue-500 pl-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:"1. Instrumentation Amplifier (AD623)"}),s.jsx("p",{className:"text-sm text-gray-600",children:"High CMRR differential amplifier with gain set by Rg"})]}),s.jsxs("div",{className:"border-l-4 border-green-500 pl-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:"2. High-Pass Filter (0.5 Hz)"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Removes DC offset and baseline drift"})]}),s.jsxs("div",{className:"border-l-4 border-orange-500 pl-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:"3. Low-Pass Filter (40 Hz)"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Reduces high-frequency noise and artifacts"})]}),s.jsxs("div",{className:"border-l-4 border-purple-500 pl-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:"4. Reference Voltage (Vcc/2)"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Provides stable mid-supply reference"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Design Calculations"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Gain Calculation:"}),s.jsx("p",{className:"text-sm text-gray-600",children:"G = 1 + (49.4kΩ / Rg)"}),s.jsx("p",{className:"text-sm text-gray-600",children:"G = 1 + (49.4kΩ / 100Ω) = 495 ≈ 500"})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("p",{className:"text-sm font-medium text-gray-700",children:"HPF Cutoff Frequency:"}),s.jsx("p",{className:"text-sm text-gray-600",children:"fc = 1 / (2π × R_hpf × C_hpf)"}),s.jsx("p",{className:"text-sm text-gray-600",children:"fc = 1 / (2π × 1MΩ × 0.33μF) = 0.48 Hz"})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("p",{className:"text-sm font-medium text-gray-700",children:"LPF Cutoff Frequency:"}),s.jsx("p",{className:"text-sm text-gray-600",children:"fc = 1 / (2π × R_lpf × C_lpf)"}),s.jsx("p",{className:"text-sm text-gray-600",children:"fc = 1 / (2π × 4kΩ × 1μF) = 39.8 Hz"})]})]})]})]})]}),e==="components"&&s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Component List & Specifications"}),s.jsx(Mm,{components:r})]}),e==="waveforms"&&s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Expected ECG Waveforms"}),s.jsx(Om,{type:"ecg",title:"Normal Sinus Rhythm",description:"Typical ECG waveform showing P-QRS-T complex"})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Electrode Placement"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-4 h-4 bg-red-500 rounded-full"}),s.jsxs("span",{className:"text-gray-700",children:[s.jsx("strong",{children:"LA (Left Arm):"})," Positive electrode"]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-4 h-4 bg-blue-500 rounded-full"}),s.jsxs("span",{className:"text-gray-700",children:[s.jsx("strong",{children:"RA (Right Arm):"})," Negative electrode"]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-4 h-4 bg-green-500 rounded-full"}),s.jsxs("span",{className:"text-gray-700",children:[s.jsx("strong",{children:"RL (Right Leg):"})," Reference electrode"]})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Waveform Components"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"border-l-4 border-blue-500 pl-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:"P Wave"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Atrial depolarization, 0.08-0.12s duration"})]}),s.jsxs("div",{className:"border-l-4 border-red-500 pl-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:"QRS Complex"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Ventricular depolarization, 0.06-0.10s duration"})]}),s.jsxs("div",{className:"border-l-4 border-green-500 pl-4",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:"T Wave"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Ventricular repolarization, 0.16-0.20s duration"})]})]})]})]})]}),e==="results"&&s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Measurement Results & Analysis"}),s.jsx("div",{className:"space-y-6",children:s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full table-auto",children:[s.jsx("thead",{children:s.jsxs("tr",{className:"bg-gray-50",children:[s.jsx("th",{className:"px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Parameter"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Measured Value"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Expected Range"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Notes"})]})}),s.jsxs("tbody",{className:"divide-y divide-gray-200",children:[s.jsxs("tr",{children:[s.jsx("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Supply Voltage (Vcc)"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"5.0 V"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"4.5 - 5.5 V"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Within specifications"})]}),s.jsxs("tr",{children:[s.jsx("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Reference Voltage (Vref)"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"2.5 V"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"2.4 - 2.6 V"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Stable reference"})]}),s.jsxs("tr",{children:[s.jsx("td",{className:"px-4 py-3 text-sm text-gray-900",children:"Heart Rate"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"72 bpm"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"60 - 100 bpm"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Normal sinus rhythm"})]}),s.jsxs("tr",{children:[s.jsx("td",{className:"px-4 py-3 text-sm text-gray-900",children:"R-R Interval"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"833 ms"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"600 - 1000 ms"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Consistent timing"})]}),s.jsxs("tr",{children:[s.jsx("td",{className:"px-4 py-3 text-sm text-gray-900",children:"QRS Amplitude"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"1.2 mV"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"0.5 - 2.0 mV"}),s.jsx("td",{className:"px-4 py-3 text-sm text-gray-600",children:"Good signal quality"})]})]})]})})})]})]})]})},Dm=()=>s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 mb-8",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[s.jsx("div",{className:"p-3 bg-orange-100 rounded-xl",children:s.jsx($l,{className:"h-8 w-8 text-orange-600"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"EMG - Electromyogram"}),s.jsx("p",{className:"text-lg text-gray-600",children:"Surface muscle electrical activity recording"})]})]}),s.jsxs("div",{className:"bg-orange-50 p-6 rounded-lg",children:[s.jsx("h3",{className:"text-lg font-semibold text-orange-800 mb-2",children:"Coming Soon"}),s.jsx("p",{className:"text-orange-700",children:"Complete EMG experiment documentation including surface electrode placement, signal filtering for muscle activity, and RMS amplitude analysis across different contraction levels will be available soon."})]})]})}),Fm=()=>s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 mb-8",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[s.jsx("div",{className:"p-3 bg-purple-100 rounded-xl",children:s.jsx(ud,{className:"h-8 w-8 text-purple-600"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"EEG - Electroencephalogram"}),s.jsx("p",{className:"text-lg text-gray-600",children:"Brain electrical activity recording"})]})]}),s.jsxs("div",{className:"bg-purple-50 p-6 rounded-lg",children:[s.jsx("h3",{className:"text-lg font-semibold text-purple-800 mb-2",children:"Coming Soon"}),s.jsx("p",{className:"text-purple-700",children:"Complete EEG experiment documentation including low-noise amplification, alpha rhythm detection, artifact filtering, and 10-20 electrode placement system will be available soon."})]})]})}),Am=()=>s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 mb-8",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[s.jsx("div",{className:"p-3 bg-blue-100 rounded-xl",children:s.jsx(ms,{className:"h-8 w-8 text-blue-600"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"EOG - Electrooculogram"}),s.jsx("p",{className:"text-lg text-gray-600",children:"Eye movement tracking system"})]})]}),s.jsxs("div",{className:"bg-blue-50 p-6 rounded-lg",children:[s.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"Coming Soon"}),s.jsx("p",{className:"text-blue-700",children:"Complete EOG experiment documentation including dual-channel recording for horizontal and vertical eye movements, blink detection, and electrode placement around the eyes will be available soon."})]})]})}),$m=()=>s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 mb-8",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[s.jsx("div",{className:"p-3 bg-green-100 rounded-xl",children:s.jsx(ps,{className:"h-8 w-8 text-green-600"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"ENG - Electroneurogram"}),s.jsx("p",{className:"text-lg text-gray-600",children:"Nerve conduction velocity measurement"})]})]}),s.jsxs("div",{className:"bg-green-50 p-6 rounded-lg",children:[s.jsx("h3",{className:"text-lg font-semibold text-green-800 mb-2",children:"Coming Soon"}),s.jsx("p",{className:"text-green-700",children:"Complete ENG experiment documentation including SNAP recording, nerve stimulation protocols, latency measurements, and conduction velocity calculations will be available soon."})]})]})}),Um=()=>s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 mb-8",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[s.jsx("div",{className:"p-3 bg-teal-100 rounded-xl",children:s.jsx(ad,{className:"h-8 w-8 text-teal-600"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"EGG - Electrogastrogram"}),s.jsx("p",{className:"text-lg text-gray-600",children:"Gastric electrical activity recording"})]})]}),s.jsxs("div",{className:"bg-teal-50 p-6 rounded-lg",children:[s.jsx("h3",{className:"text-lg font-semibold text-teal-800 mb-2",children:"Coming Soon"}),s.jsx("p",{className:"text-teal-700",children:"Complete EGG experiment documentation including slow wave detection, respiratory artifact filtering, gastric rhythm analysis, and cutaneous electrode placement will be available soon."})]})]})}),Wm=()=>s.jsx("footer",{className:"bg-gray-900 text-white mt-16",children:s.jsxs("div",{className:"container mx-auto px-4 py-12",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold mb-4",children:"BioMed Lab"}),s.jsx("p",{className:"text-gray-400 mb-4",children:"Comprehensive documentation for biomedical signal recording experiments and circuit designs."}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:s.jsx(Em,{className:"h-5 w-5"})}),s.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:s.jsx(jm,{className:"h-5 w-5"})}),s.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:s.jsx(Cm,{className:"h-5 w-5"})})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Experiments"}),s.jsxs("ul",{className:"space-y-2 text-gray-400",children:[s.jsx("li",{children:s.jsx("a",{href:"/ecg",className:"hover:text-white transition-colors",children:"ECG - Electrocardiogram"})}),s.jsx("li",{children:s.jsx("a",{href:"/emg",className:"hover:text-white transition-colors",children:"EMG - Electromyogram"})}),s.jsx("li",{children:s.jsx("a",{href:"/eeg",className:"hover:text-white transition-colors",children:"EEG - Electroencephalogram"})}),s.jsx("li",{children:s.jsx("a",{href:"/eog",className:"hover:text-white transition-colors",children:"EOG - Electrooculogram"})}),s.jsx("li",{children:s.jsx("a",{href:"/eng",className:"hover:text-white transition-colors",children:"ENG - Electroneurogram"})}),s.jsx("li",{children:s.jsx("a",{href:"/egg",className:"hover:text-white transition-colors",children:"EGG - Electrogastrogram"})})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Resources"}),s.jsxs("ul",{className:"space-y-2 text-gray-400",children:[s.jsx("li",{children:s.jsx("a",{href:"#",className:"hover:text-white transition-colors",children:"Circuit Simulation"})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"hover:text-white transition-colors",children:"Component Database"})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"hover:text-white transition-colors",children:"Safety Guidelines"})}),s.jsx("li",{children:s.jsx("a",{href:"#",className:"hover:text-white transition-colors",children:"Troubleshooting"})})]})]})]}),s.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:s.jsx("p",{children:"© 2025 BioMed Lab. All rights reserved. Educational use only."})})]})});function Vm(){return s.jsx(um,{children:s.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:[s.jsx(_m,{}),s.jsx("main",{className:"container mx-auto px-4 py-8",children:s.jsxs($0,{children:[s.jsx(it,{path:"/",element:s.jsx(zm,{})}),s.jsx(it,{path:"/ecg",element:s.jsx(Im,{})}),s.jsx(it,{path:"/emg",element:s.jsx(Dm,{})}),s.jsx(it,{path:"/eeg",element:s.jsx(Fm,{})}),s.jsx(it,{path:"/eog",element:s.jsx(Am,{})}),s.jsx(it,{path:"/eng",element:s.jsx($m,{})}),s.jsx(it,{path:"/egg",element:s.jsx(Um,{})})]})}),s.jsx(Wm,{})]})})}Gc(document.getElementById("root")).render(s.jsx(y.StrictMode,{children:s.jsx(Vm,{})}));
