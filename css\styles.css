/* ECG Lab - Main Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --text-color: #374151;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

body {
    font-family: '<PERSON><PERSON>wal', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: var(--shadow);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-logo i {
    margin-left: 10px;
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 20px 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #059669;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: var(--primary-color);
}

.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* ECG Monitor */
.ecg-monitor {
    background: var(--dark-color);
    border-radius: 20px;
    padding: 20px;
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 100%;
}

.monitor-screen {
    background: #000;
    border-radius: 10px;
    padding: 20px;
    position: relative;
    margin-bottom: 20px;
}

.monitor-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(0, 255, 0, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    border-radius: 10px;
}

#ecgCanvas {
    width: 100%;
    height: auto;
    display: block;
}

.monitor-controls {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.control-group {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
}

.control-group label {
    display: block;
    font-size: 0.875rem;
    color: #9ca3af;
    margin-bottom: 5px;
}

.control-group span {
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
}

.status-active {
    color: var(--secondary-color) !important;
}

/* Sections */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--dark-color);
}

.measurements-section,
.circuits-section,
.analysis-section {
    padding: 80px 0;
}

.measurements-section {
    background: white;
}

.circuits-section {
    background: var(--light-color);
}

.analysis-section {
    background: white;
}

/* Measurements Grid */
.measurements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.measurement-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.measurement-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: white;
}

.measurement-card:nth-child(1) .card-icon {
    background: var(--danger-color);
}

.measurement-card:nth-child(2) .card-icon {
    background: var(--accent-color);
}

.measurement-card:nth-child(3) .card-icon {
    background: var(--primary-color);
}

.measurement-card:nth-child(4) .card-icon {
    background: var(--secondary-color);
}

.card-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.measurement-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.measurement-unit {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.measurement-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.trend-up {
    color: var(--secondary-color);
}

.trend-down {
    color: var(--danger-color);
}

.trend-stable {
    color: var(--accent-color);
}

/* Live Waveform */
.live-waveform {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.live-waveform h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.waveform-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    padding: 20px;
}

#liveEcgCanvas {
    width: 100%;
    height: auto;
    display: block;
}

.waveform-controls {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    gap: 10px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #9ca3af;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* Circuit Diagrams */
.circuit-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    gap: 1rem;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 12px 24px;
    border: 2px solid var(--primary-color);
    background: white;
    color: var(--primary-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.tab-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-lg);
}

.circuit-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.circuit-diagram {
    display: none;
}

.circuit-diagram.active {
    display: block;
}

.diagram-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.diagram-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.diagram-controls {
    display: flex;
    gap: 0.5rem;
}

.svg-container {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 2rem;
    overflow: auto;
}

.circuit-svg {
    width: 100%;
    height: auto;
    min-height: 400px;
    transition: transform 0.3s ease;
}

.component-info {
    background: var(--light-color);
    border-radius: 8px;
    padding: 1.5rem;
}

.component-info h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.components-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.component-item {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    transition: all 0.3s ease;
}

.component-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

/* Analysis Section */
.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.analysis-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.analysis-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.analysis-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
    text-align: center;
}

.analysis-card canvas {
    width: 100%;
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--light-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.stat-item label {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.stat-item span {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--primary-color);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 20px 40px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-visual {
        margin-top: 2rem;
    }

    .measurements-grid {
        grid-template-columns: 1fr;
    }

    .monitor-controls {
        grid-template-columns: 1fr;
    }

    .circuit-tabs {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 100%;
        max-width: 200px;
    }

    .diagram-header {
        flex-direction: column;
        text-align: center;
    }

    .analysis-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .components-list {
        grid-template-columns: 1fr;
    }
}
