<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Lab Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
            background: #000;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h1>ECG Lab Debug Panel</h1>
        <div id="status" class="status info">جاري التحميل...</div>
        
        <h2>اختبار الملفات</h2>
        <div id="fileStatus"></div>
        
        <h2>اختبار Canvas</h2>
        <canvas id="testCanvas" width="600" height="200"></canvas>
        <br>
        <button class="btn" onclick="testCanvas()">اختبار Canvas</button>
        <button class="btn" onclick="testECGSimulation()">اختبار محاكي ECG</button>
        <button class="btn" onclick="clearLog()">مسح السجل</button>
        
        <h2>سجل الأحداث</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        let logElement;
        
        function log(message, type = 'info') {
            if (!logElement) logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}]`, message);
        }
        
        function clearLog() {
            if (logElement) logElement.textContent = '';
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function testCanvas() {
            log('بدء اختبار Canvas...');
            
            const canvas = document.getElementById('testCanvas');
            if (!canvas) {
                log('Canvas غير موجود!', 'error');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                log('فشل في الحصول على context!', 'error');
                return;
            }
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw test ECG wave
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            const centerY = canvas.height / 2;
            for (let x = 0; x < canvas.width; x++) {
                const t = x * 0.01;
                let y = centerY;
                
                // Simple ECG-like wave
                y += Math.sin(t * 10) * 20; // Base sine wave
                y += Math.sin(t * 50) * 10 * Math.exp(-Math.pow((t % 1) - 0.5, 2) * 20); // QRS spikes
                
                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
            log('تم رسم موجة اختبار بنجاح', 'success');
        }
        
        function testECGSimulation() {
            log('اختبار محاكي ECG...');
            
            if (typeof ECGSimulator === 'undefined') {
                log('فئة ECGSimulator غير محملة!', 'error');
                return;
            }
            
            try {
                const simulator = new ECGSimulator();
                log('تم إنشاء محاكي ECG بنجاح', 'success');
                
                if (simulator.heroCanvas) {
                    log('تم العثور على Hero Canvas', 'success');
                } else {
                    log('Hero Canvas غير موجود', 'warning');
                }
                
                if (simulator.liveCanvas) {
                    log('تم العثور على Live Canvas', 'success');
                } else {
                    log('Live Canvas غير موجود', 'warning');
                }
                
            } catch (error) {
                log(`خطأ في إنشاء محاكي ECG: ${error.message}`, 'error');
            }
        }
        
        function checkFiles() {
            log('فحص الملفات...');
            
            const files = [
                'css/styles.css',
                'css/animations.css',
                'js/ecg-simulation.js',
                'js/circuit-diagrams.js',
                'js/measurements.js',
                'js/main.js'
            ];
            
            const fileStatus = document.getElementById('fileStatus');
            fileStatus.innerHTML = '';
            
            let loadedCount = 0;
            
            files.forEach(file => {
                const div = document.createElement('div');
                div.style.margin = '5px 0';
                
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            div.innerHTML = `✅ ${file}`;
                            div.style.color = 'green';
                            loadedCount++;
                            log(`تم تحميل ${file}`, 'success');
                        } else {
                            div.innerHTML = `❌ ${file} (${response.status})`;
                            div.style.color = 'red';
                            log(`فشل تحميل ${file}: ${response.status}`, 'error');
                        }
                        
                        if (loadedCount === files.length) {
                            updateStatus('تم تحميل جميع الملفات بنجاح', 'success');
                        }
                    })
                    .catch(error => {
                        div.innerHTML = `❌ ${file} (غير موجود)`;
                        div.style.color = 'red';
                        log(`خطأ في تحميل ${file}: ${error.message}`, 'error');
                    });
                
                fileStatus.appendChild(div);
            });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل DOM');
            updateStatus('تم تحميل الصفحة', 'success');
            checkFiles();
            
            // Test canvas after a delay
            setTimeout(() => {
                testCanvas();
            }, 1000);
        });
        
        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            log(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            log(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            log(args.join(' '), 'warning');
        };
    </script>
    
    <!-- Load ECG Lab scripts for testing -->
    <script src="js/ecg-simulation.js"></script>
    <script src="js/circuit-diagrams.js"></script>
    <script src="js/measurements.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
