import React from 'react';
import { Atom as Stomach } from 'lucide-react';

const EGGExperiment = () => {
  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div className="flex items-center space-x-4 mb-6">
          <div className="p-3 bg-teal-100 rounded-xl">
            <Stomach className="h-8 w-8 text-teal-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">EGG - Electrogastrogram</h1>
            <p className="text-lg text-gray-600">Gastric electrical activity recording</p>
          </div>
        </div>
        
        <div className="bg-teal-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-teal-800 mb-2">Coming Soon</h3>
          <p className="text-teal-700">
            Complete EGG experiment documentation including slow wave detection, 
            respiratory artifact filtering, gastric rhythm analysis, and 
            cutaneous electrode placement will be available soon.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EGGExperiment;