import React from 'react';

interface Component {
  reference: string;
  type: string;
  value: string;
  specs: string;
}

interface ComponentTableProps {
  components: Component[];
}

const ComponentTable: React.FC<ComponentTableProps> = ({ components }) => {
  return (
    <div className="overflow-x-auto">
      <table className="w-full table-auto">
        <thead>
          <tr className="bg-gray-50">
            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Reference</th>
            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Component Type</th>
            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Value/Part Number</th>
            <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Specifications</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {components.map((component, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-3 text-sm font-medium text-gray-900">{component.reference}</td>
              <td className="px-4 py-3 text-sm text-gray-600">{component.type}</td>
              <td className="px-4 py-3 text-sm text-gray-600 font-mono">{component.value}</td>
              <td className="px-4 py-3 text-sm text-gray-600">{component.specs}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ComponentTable;