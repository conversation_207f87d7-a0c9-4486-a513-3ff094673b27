<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مختبر تخطيط كهربية القلب (ECG) - تفاعلي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header Styles */
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            position: relative;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.2); }
            to { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.4); }
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        /* Navigation */
        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-tab {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 2px solid rgba(255,255,255,0.2);
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .nav-tab.active,
        .nav-tab:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.4);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* Main Content Grid */
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        /* Card Styles */
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8rem;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        /* ECG Monitor Styles */
        .ecg-monitor {
            background: #000;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
            border: 3px solid #333;
        }

        .monitor-screen {
            position: relative;
            height: 250px;
            background: #001100;
            border-radius: 10px;
            overflow: hidden;
        }

        .ecg-canvas {
            width: 100%;
            height: 100%;
            border-radius: 10px;
        }

        .monitor-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0,255,0,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,255,0,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
        }

        /* Control Styles */
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            background: linear-gradient(45deg, #5a6fd8, #6a42a0);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
        }

        .btn.success {
            background: linear-gradient(45deg, #51cf66, #40c057);
        }

        /* Measurements Grid */
        .measurements {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .measurement-item {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .measurement-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .measurement-item:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff, #e6f0ff);
            transform: translateY(-2px);
        }

        .measurement-value {
            font-size: 2.2rem;
            font-weight: bold;
            color: #667eea;
            display: block;
            margin-bottom: 5px;
        }

        .measurement-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .measurement-unit {
            font-size: 0.8rem;
            color: #999;
            margin-top: 2px;
        }

        /* Status Indicator */
        .status-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(40, 167, 69, 0.9);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .status-indicator.warning {
            background: rgba(255, 193, 7, 0.9);
            color: #333;
        }

        .status-indicator.error {
            background: rgba(220, 53, 69, 0.9);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2.5rem;
            }

            .measurements {
                grid-template-columns: repeat(2, 1fr);
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 200px;
            }
        }

        /* Animation Classes */
        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .heartbeat {
            animation: heartbeat 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }
    </style>
</head>
<body>
    <!-- Status Indicator -->
    <div class="status-indicator" id="status">
        <i class="fas fa-circle pulse" style="color: #28a745;"></i> جاري التحميل...
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header slide-in">
            <h1><i class="fas fa-heartbeat heartbeat"></i> مختبر تخطيط كهربية القلب التفاعلي</h1>
            <p>نظام متقدم لقياس وتحليل الإشارات الكهروفسيولوجية مع أدوات تفاعلية ومحاكاة في الوقت الفعلي</p>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showSection('monitor')">
                <i class="fas fa-chart-line"></i> المراقب المباشر
            </button>
            <button class="nav-tab" onclick="showSection('circuits')">
                <i class="fas fa-microchip"></i> الدوائر الإلكترونية
            </button>
            <button class="nav-tab" onclick="showSection('analysis')">
                <i class="fas fa-analytics"></i> التحليل والقياسات
            </button>
            <button class="nav-tab" onclick="showSection('education')">
                <i class="fas fa-graduation-cap"></i> المواد التعليمية
            </button>
        </div>

        <!-- Monitor Section -->
        <div id="monitor-section" class="section active">
            <div class="main-grid">
                <!-- ECG Monitor -->
                <div class="card slide-in">
                    <h2><i class="fas fa-tv"></i> مراقب ECG المباشر</h2>
                    <div class="ecg-monitor">
                        <div class="monitor-screen">
                            <canvas id="ecgCanvas" class="ecg-canvas" width="600" height="250"></canvas>
                            <div class="monitor-grid"></div>
                        </div>
                    </div>
                    <div class="controls">
                        <button class="btn success" onclick="toggleSimulation()" id="toggleBtn">
                            <i class="fas fa-play" id="playIcon"></i>
                            <span id="playText">بدء المراقبة</span>
                        </button>
                        <button class="btn" onclick="resetSimulation()">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button class="btn" onclick="saveECG()">
                            <i class="fas fa-save"></i> حفظ البيانات
                        </button>
                        <button class="btn" onclick="adjustSettings()">
                            <i class="fas fa-cog"></i> الإعدادات
                        </button>
                    </div>
                </div>

                <!-- Live Measurements -->
                <div class="card slide-in">
                    <h2><i class="fas fa-tachometer-alt"></i> القياسات المباشرة</h2>
                    <div class="measurements">
                        <div class="measurement-item">
                            <span class="measurement-value" id="heartRate">--</span>
                            <div class="measurement-label">معدل القلب</div>
                            <div class="measurement-unit">نبضة/دقيقة</div>
                        </div>
                        <div class="measurement-item">
                            <span class="measurement-value" id="voltage">--</span>
                            <div class="measurement-label">الجهد</div>
                            <div class="measurement-unit">ميلي فولت</div>
                        </div>
                        <div class="measurement-item">
                            <span class="measurement-value" id="rrInterval">--</span>
                            <div class="measurement-label">فترة R-R</div>
                            <div class="measurement-unit">مللي ثانية</div>
                        </div>
                        <div class="measurement-item">
                            <span class="measurement-value" id="quality">--</span>
                            <div class="measurement-label">جودة الإشارة</div>
                            <div class="measurement-unit">%</div>
                        </div>
                        <div class="measurement-item">
                            <span class="measurement-value" id="prInterval">--</span>
                            <div class="measurement-label">فترة PR</div>
                            <div class="measurement-unit">مللي ثانية</div>
                        </div>
                        <div class="measurement-item">
                            <span class="measurement-value" id="qtInterval">--</span>
                            <div class="measurement-label">فترة QT</div>
                            <div class="measurement-unit">مللي ثانية</div>
                        </div>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <button class="btn" onclick="exportMeasurements()">
                            <i class="fas fa-download"></i> تصدير القياسات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Waveform Analysis -->
            <div class="card slide-in" style="margin-top: 30px;">
                <h2><i class="fas fa-wave-square"></i> تحليل الموجة التفصيلي</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3 style="margin-bottom: 15px; color: #667eea;">مكونات الموجة</h3>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                            <div style="margin-bottom: 10px;">
                                <strong>موجة P:</strong> <span id="pWaveInfo">انقباض الأذينين</span>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <strong>مركب QRS:</strong> <span id="qrsInfo">انقباض البطينين</span>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <strong>موجة T:</strong> <span id="tWaveInfo">استرخاء البطينين</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 style="margin-bottom: 15px; color: #667eea;">الحالة الطبية</h3>
                        <div style="background: #f0fdf4; padding: 15px; border-radius: 10px; border-right: 4px solid #10b981;">
                            <div id="medicalStatus">
                                <i class="fas fa-check-circle" style="color: #10b981;"></i>
                                <strong>إيقاع طبيعي</strong><br>
                                <small>جميع المعايير ضمن النطاق الطبيعي</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Circuits Section -->
        <div id="circuits-section" class="section" style="display: none;">
            <div class="card slide-in">
                <h2><i class="fas fa-microchip"></i> الدوائر الإلكترونية التفاعلية</h2>

                <!-- Circuit Navigation -->
                <div style="display: flex; justify-content: center; gap: 10px; margin-bottom: 30px; flex-wrap: wrap;">
                    <button class="btn" onclick="showCircuit('complete')" id="completeBtn">
                        <i class="fas fa-sitemap"></i> الدائرة الكاملة
                    </button>
                    <button class="btn" onclick="showCircuit('amplifier')" id="amplifierBtn">
                        <i class="fas fa-volume-up"></i> مضخم الإشارة
                    </button>
                    <button class="btn" onclick="showCircuit('filter')" id="filterBtn">
                        <i class="fas fa-filter"></i> المرشحات
                    </button>
                    <button class="btn" onclick="showCircuit('reference')" id="referenceBtn">
                        <i class="fas fa-anchor"></i> دائرة المرجع
                    </button>
                </div>

                <!-- Circuit Display -->
                <div id="circuitDisplay" style="background: #f8f9fa; border-radius: 15px; padding: 30px; min-height: 400px; text-align: center;">
                    <div style="color: #666;">
                        <i class="fas fa-microchip" style="font-size: 4rem; margin-bottom: 20px; color: #667eea;"></i>
                        <h3>اختر دائرة من الأزرار أعلاه لعرض التفاصيل التفاعلية</h3>
                        <p style="margin-top: 10px;">ستتمكن من رؤية المخططات التفصيلية والمواصفات التقنية</p>
                    </div>
                </div>

                <!-- Circuit Controls -->
                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn" onclick="zoomIn()" id="zoomInBtn" style="display: none;">
                        <i class="fas fa-search-plus"></i> تكبير
                    </button>
                    <button class="btn" onclick="zoomOut()" id="zoomOutBtn" style="display: none;">
                        <i class="fas fa-search-minus"></i> تصغير
                    </button>
                    <button class="btn" onclick="downloadCircuit()" id="downloadBtn" style="display: none;">
                        <i class="fas fa-download"></i> تحميل المخطط
                    </button>
                </div>
            </div>

            <!-- Components Table -->
            <div class="card slide-in" style="margin-top: 30px;">
                <h2><i class="fas fa-list"></i> قائمة المكونات والمواصفات</h2>
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                        <thead>
                            <tr style="background: #667eea; color: white;">
                                <th style="padding: 15px; text-align: right; border: 1px solid #ddd;">الرمز</th>
                                <th style="padding: 15px; text-align: right; border: 1px solid #ddd;">نوع المكون</th>
                                <th style="padding: 15px; text-align: right; border: 1px solid #ddd;">القيمة/رقم القطعة</th>
                                <th style="padding: 15px; text-align: right; border: 1px solid #ddd;">المواصفات</th>
                                <th style="padding: 15px; text-align: right; border: 1px solid #ddd;">الوظيفة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background: #f8f9fa;">
                                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">U1</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مضخم أجهزة</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">AD623AN</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">DIP-8, Gain 100, CMRR > 80dB</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">تضخيم الإشارة التفاضلية</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">U2A</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مضخم عمليات</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">MCP6002</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">DIP-8, Single Supply, Rail-to-Rail</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مخزن مؤقت لجهد المرجع</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">R1, R2</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مقاومة</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">10kΩ</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">1%, 1/4W, Carbon Film</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مقسم جهد للمرجع</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Rg</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مقاومة</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">1kΩ</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">1%, 1/4W, Metal Film</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">تحديد كسب المضخم</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">R_hpf</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مقاومة</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">330kΩ</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">1%, 1/4W</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مرشح عالي التمرير</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">C_hpf</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مكثف</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">1µF</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">16V, Ceramic, X7R</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مرشح عالي التمرير (fc = 0.5Hz)</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">R_lpf</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مقاومة</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">10kΩ</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">1%, 1/4W</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مرشح منخفض التمرير</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">C_lpf</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مكثف</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">0.1µF</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">16V, Ceramic</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">مرشح منخفض التمرير (fc = 150Hz)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!-- Analysis Section -->
        <div id="analysis-section" class="section" style="display: none;">
            <div class="main-grid">
                <!-- Frequency Analysis -->
                <div class="card slide-in">
                    <h2><i class="fas fa-chart-bar"></i> تحليل الطيف الترددي</h2>
                    <div style="background: #000; border-radius: 10px; padding: 20px; margin: 20px 0;">
                        <canvas id="spectrumCanvas" width="400" height="200" style="width: 100%; height: 200px; border-radius: 5px;"></canvas>
                    </div>
                    <div style="text-align: center;">
                        <button class="btn" onclick="updateSpectrum()">
                            <i class="fas fa-sync"></i> تحديث التحليل
                        </button>
                    </div>
                </div>

                <!-- HRV Analysis -->
                <div class="card slide-in">
                    <h2><i class="fas fa-heartbeat"></i> تحليل تغيرات معدل القلب (HRV)</h2>
                    <div style="background: #000; border-radius: 10px; padding: 20px; margin: 20px 0;">
                        <canvas id="hrvCanvas" width="400" height="200" style="width: 100%; height: 200px; border-radius: 5px;"></canvas>
                    </div>
                    <div class="measurements" style="margin-top: 20px;">
                        <div class="measurement-item">
                            <span class="measurement-value" id="rmssd">--</span>
                            <div class="measurement-label">RMSSD</div>
                            <div class="measurement-unit">مللي ثانية</div>
                        </div>
                        <div class="measurement-item">
                            <span class="measurement-value" id="sdnn">--</span>
                            <div class="measurement-label">SDNN</div>
                            <div class="measurement-unit">مللي ثانية</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistical Analysis -->
            <div class="card slide-in" style="margin-top: 30px;">
                <h2><i class="fas fa-calculator"></i> التحليل الإحصائي المتقدم</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                        <h3 style="color: #667eea; margin-bottom: 15px;">إحصائيات الوقت</h3>
                        <div style="margin-bottom: 10px;">
                            <strong>متوسط R-R:</strong> <span id="avgRR">--</span> ms
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>أدنى R-R:</strong> <span id="minRR">--</span> ms
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>أعلى R-R:</strong> <span id="maxRR">--</span> ms
                        </div>
                    </div>

                    <div style="background: #f0fdf4; padding: 20px; border-radius: 10px;">
                        <h3 style="color: #10b981; margin-bottom: 15px;">إحصائيات التردد</h3>
                        <div style="margin-bottom: 10px;">
                            <strong>LF Power:</strong> <span id="lfPower">--</span> ms²
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>HF Power:</strong> <span id="hfPower">--</span> ms²
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>LF/HF Ratio:</strong> <span id="lfhfRatio">--</span>
                        </div>
                    </div>

                    <div style="background: #fef7f0; padding: 20px; border-radius: 10px;">
                        <h3 style="color: #f59e0b; margin-bottom: 15px;">مؤشرات الصحة</h3>
                        <div style="margin-bottom: 10px;">
                            <strong>مؤشر الإجهاد:</strong> <span id="stressIndex">--</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>التوازن العصبي:</strong> <span id="autonomicBalance">--</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>الحالة العامة:</strong> <span id="overallHealth">--</span>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn" onclick="generateReport()">
                        <i class="fas fa-file-alt"></i> إنشاء تقرير شامل
                    </button>
                    <button class="btn" onclick="exportData()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                </div>
            </div>
        </div>
        <!-- Education Section -->
        <div id="education-section" class="section" style="display: none;">
            <div class="card slide-in">
                <h2><i class="fas fa-graduation-cap"></i> المواد التعليمية التفاعلية</h2>

                <!-- Educational Topics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #667eea;">
                        <h3 style="color: #667eea; margin-bottom: 15px;">
                            <i class="fas fa-heart"></i> فسيولوجيا القلب
                        </h3>
                        <p style="margin-bottom: 15px;">تعلم كيف يعمل القلب وكيف تتولد الإشارات الكهربائية</p>
                        <button class="btn" onclick="showEducationalContent('physiology')">
                            <i class="fas fa-play"></i> بدء التعلم
                        </button>
                    </div>

                    <div style="background: #f0fdf4; padding: 20px; border-radius: 10px; border-right: 4px solid #10b981;">
                        <h3 style="color: #10b981; margin-bottom: 15px;">
                            <i class="fas fa-wave-square"></i> تفسير موجات ECG
                        </h3>
                        <p style="margin-bottom: 15px;">فهم مكونات موجة ECG وما تعنيه كل موجة</p>
                        <button class="btn" onclick="showEducationalContent('waves')">
                            <i class="fas fa-play"></i> بدء التعلم
                        </button>
                    </div>

                    <div style="background: #fef7f0; padding: 20px; border-radius: 10px; border-right: 4px solid #f59e0b;">
                        <h3 style="color: #f59e0b; margin-bottom: 15px;">
                            <i class="fas fa-microchip"></i> الدوائر الإلكترونية
                        </h3>
                        <p style="margin-bottom: 15px;">تعلم كيفية تصميم وبناء دوائر تسجيل ECG</p>
                        <button class="btn" onclick="showEducationalContent('circuits')">
                            <i class="fas fa-play"></i> بدء التعلم
                        </button>
                    </div>

                    <div style="background: #f0f9ff; padding: 20px; border-radius: 10px; border-right: 4px solid #3b82f6;">
                        <h3 style="color: #3b82f6; margin-bottom: 15px;">
                            <i class="fas fa-stethoscope"></i> التطبيقات الطبية
                        </h3>
                        <p style="margin-bottom: 15px;">استخدامات ECG في التشخيص الطبي</p>
                        <button class="btn" onclick="showEducationalContent('medical')">
                            <i class="fas fa-play"></i> بدء التعلم
                        </button>
                    </div>
                </div>

                <!-- Educational Content Display -->
                <div id="educationalContent" style="margin-top: 30px; padding: 20px; background: white; border-radius: 10px; border: 2px solid #e5e7eb; display: none;">
                    <!-- Content will be loaded here -->
                </div>
            </div>

            <!-- Interactive Quiz -->
            <div class="card slide-in" style="margin-top: 30px;">
                <h2><i class="fas fa-question-circle"></i> اختبار تفاعلي</h2>
                <div id="quizContainer" style="text-align: center; padding: 20px;">
                    <p style="margin-bottom: 20px;">اختبر معرفتك حول تخطيط كهربية القلب</p>
                    <button class="btn" onclick="startQuiz()">
                        <i class="fas fa-play"></i> بدء الاختبار
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 50px; padding: 30px; color: rgba(255,255,255,0.8);">
            <p style="font-size: 1.1rem; margin-bottom: 10px;">© 2024 مختبر الإشارات الحيوية - كلية الهندسة الطبية الحيوية</p>
            <p style="font-size: 0.9rem;">تم التطوير باستخدام تقنيات الويب المتقدمة مع محاكاة في الوقت الفعلي</p>
        </div>
    </div>

    <script>
        // Global Variables
        let ecgSimulator = null;
        let isRunning = false;
        let currentSection = 'monitor';
        let currentCircuit = null;
        let animationId = null;
        let ecgData = [];
        let rrIntervals = [];

        // ECG Simulator Class
        class ECGSimulator {
            constructor() {
                this.canvas = document.getElementById('ecgCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.time = 0;
                this.heartRate = 75;
                this.amplitude = 1.2;
                this.isRunning = false;
                this.dataPoints = [];
                this.lastRTime = 0;

                this.setupCanvas();
                this.updateStatus('تم تهيئة المحاكي بنجاح', 'success');
            }

            setupCanvas() {
                const rect = this.canvas.getBoundingClientRect();
                this.canvas.width = rect.width * 2; // High DPI
                this.canvas.height = rect.height * 2;
                this.ctx.scale(2, 2);
                this.canvas.style.width = rect.width + 'px';
                this.canvas.style.height = rect.height + 'px';
            }

            start() {
                this.isRunning = true;
                this.animate();
                this.updateStatus('المراقبة نشطة', 'success');
            }

            stop() {
                this.isRunning = false;
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
                this.updateStatus('تم إيقاف المراقبة', 'warning');
            }

            reset() {
                this.time = 0;
                this.dataPoints = [];
                this.rrIntervals = [];
                ecgData = [];
                this.ctx.clearRect(0, 0, this.canvas.width/2, this.canvas.height/2);
                this.updateMeasurements();
                this.updateStatus('تم إعادة تعيين البيانات', 'info');
            }

            animate() {
                if (!this.isRunning) return;

                this.time += 0.02;
                this.drawECG();
                this.updateMeasurements();

                animationId = requestAnimationFrame(() => this.animate());
            }

            drawECG() {
                const width = this.canvas.width / 2;
                const height = this.canvas.height / 2;
                const centerY = height / 2;

                // Clear canvas with grid
                this.ctx.fillStyle = '#001100';
                this.ctx.fillRect(0, 0, width, height);

                // Draw grid
                this.ctx.strokeStyle = 'rgba(0, 255, 0, 0.1)';
                this.ctx.lineWidth = 0.5;
                for (let x = 0; x < width; x += 20) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, height);
                    this.ctx.stroke();
                }
                for (let y = 0; y < height; y += 20) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(width, y);
                    this.ctx.stroke();
                }

                // Generate ECG waveform
                const ecgValue = this.generateECGPoint(this.time);
                this.dataPoints.push({
                    time: this.time,
                    value: ecgValue,
                    x: (this.time * 50) % width
                });

                // Keep only visible points
                this.dataPoints = this.dataPoints.filter(point =>
                    point.x > (this.time * 50) % width - width &&
                    point.x < (this.time * 50) % width + 50
                );

                // Draw ECG waveform
                this.ctx.strokeStyle = '#00ff00';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();

                for (let i = 0; i < this.dataPoints.length - 1; i++) {
                    const point = this.dataPoints[i];
                    const nextPoint = this.dataPoints[i + 1];

                    const x = point.x;
                    const y = centerY - (point.value * this.amplitude * 80);
                    const nextX = nextPoint.x;
                    const nextY = centerY - (nextPoint.value * this.amplitude * 80);

                    if (i === 0) {
                        this.ctx.moveTo(x, y);
                    }
                    this.ctx.lineTo(nextX, nextY);
                }
                this.ctx.stroke();

                // Store data for analysis
                ecgData.push({
                    time: this.time,
                    value: ecgValue
                });

                // Keep only last 1000 points
                if (ecgData.length > 1000) {
                    ecgData.shift();
                }
            }

            generateECGPoint(t) {
                const bpm = this.heartRate;
                const period = 60 / bpm;
                const phase = (t % period) / period;

                let ecg = 0;

                // P wave (0.08-0.12s)
                if (phase >= 0.05 && phase <= 0.15) {
                    const pPhase = (phase - 0.05) / 0.1;
                    ecg += 0.15 * Math.sin(Math.PI * pPhase);
                }

                // QRS complex (0.06-0.10s)
                if (phase >= 0.15 && phase <= 0.25) {
                    const qrsPhase = (phase - 0.15) / 0.1;
                    if (qrsPhase < 0.3) {
                        ecg -= 0.2 * Math.sin(Math.PI * qrsPhase / 0.3); // Q wave
                    } else if (qrsPhase < 0.7) {
                        ecg += 1.2 * Math.sin(Math.PI * (qrsPhase - 0.3) / 0.4); // R wave
                        if (qrsPhase > 0.4 && qrsPhase < 0.6) {
                            this.detectRPeak(t);
                        }
                    } else {
                        ecg -= 0.3 * Math.sin(Math.PI * (qrsPhase - 0.7) / 0.3); // S wave
                    }
                }

                // T wave (0.16s)
                if (phase >= 0.35 && phase <= 0.55) {
                    const tPhase = (phase - 0.35) / 0.2;
                    ecg += 0.3 * Math.sin(Math.PI * tPhase);
                }

                // Add some noise
                ecg += (Math.random() - 0.5) * 0.02;

                return ecg;
            }

            detectRPeak(currentTime) {
                if (currentTime - this.lastRTime > 0.4) { // Minimum 400ms between R peaks
                    const rrInterval = (currentTime - this.lastRTime) * 1000;
                    if (this.lastRTime > 0) {
                        rrIntervals.push(rrInterval);
                        if (rrIntervals.length > 20) {
                            rrIntervals.shift();
                        }
                    }
                    this.lastRTime = currentTime;
                }
            }

            updateMeasurements() {
                // Heart Rate
                if (rrIntervals.length > 0) {
                    const avgRR = rrIntervals.reduce((a, b) => a + b, 0) / rrIntervals.length;
                    this.heartRate = Math.round(60000 / avgRR);
                    document.getElementById('heartRate').textContent = this.heartRate;
                    document.getElementById('rrInterval').textContent = Math.round(avgRR);
                }

                // Voltage (simulated)
                const voltage = (Math.random() * 0.5 + 0.8).toFixed(2);
                document.getElementById('voltage').textContent = voltage;

                // Signal Quality
                const quality = Math.round(85 + Math.random() * 10);
                document.getElementById('quality').textContent = quality;

                // PR Interval (simulated)
                const prInterval = Math.round(140 + Math.random() * 40);
                document.getElementById('prInterval').textContent = prInterval;

                // QT Interval (simulated)
                const qtInterval = Math.round(380 + Math.random() * 40);
                document.getElementById('qtInterval').textContent = qtInterval;

                // Update HRV analysis
                this.updateHRVAnalysis();
            }

            updateHRVAnalysis() {
                if (rrIntervals.length < 5) return;

                // RMSSD calculation
                let sumSquaredDiffs = 0;
                for (let i = 1; i < rrIntervals.length; i++) {
                    const diff = rrIntervals[i] - rrIntervals[i-1];
                    sumSquaredDiffs += diff * diff;
                }
                const rmssd = Math.sqrt(sumSquaredDiffs / (rrIntervals.length - 1));
                document.getElementById('rmssd').textContent = Math.round(rmssd);

                // SDNN calculation
                const mean = rrIntervals.reduce((a, b) => a + b, 0) / rrIntervals.length;
                const variance = rrIntervals.reduce((sum, rr) => sum + Math.pow(rr - mean, 2), 0) / rrIntervals.length;
                const sdnn = Math.sqrt(variance);
                document.getElementById('sdnn').textContent = Math.round(sdnn);

                // Update statistical displays
                document.getElementById('avgRR').textContent = Math.round(mean);
                document.getElementById('minRR').textContent = Math.round(Math.min(...rrIntervals));
                document.getElementById('maxRR').textContent = Math.round(Math.max(...rrIntervals));

                // Simulated frequency domain analysis
                document.getElementById('lfPower').textContent = Math.round(Math.random() * 1000 + 500);
                document.getElementById('hfPower').textContent = Math.round(Math.random() * 500 + 200);
                document.getElementById('lfhfRatio').textContent = (Math.random() * 2 + 1).toFixed(2);

                // Health indicators
                document.getElementById('stressIndex').textContent = Math.round(Math.random() * 50 + 25);
                document.getElementById('autonomicBalance').textContent = 'متوازن';
                document.getElementById('overallHealth').textContent = 'جيد';
            }

            updateStatus(message, type = 'info') {
                const statusEl = document.getElementById('status');
                statusEl.innerHTML = `<i class="fas fa-circle pulse"></i> ${message}`;
                statusEl.className = `status-indicator ${type}`;
            }
        }

        // Global Functions for UI Interaction
        function toggleSimulation() {
            if (!ecgSimulator) {
                ecgSimulator = new ECGSimulator();
            }

            if (isRunning) {
                ecgSimulator.stop();
                isRunning = false;
                document.getElementById('playIcon').className = 'fas fa-play';
                document.getElementById('playText').textContent = 'بدء المراقبة';
                document.getElementById('toggleBtn').className = 'btn success';
            } else {
                ecgSimulator.start();
                isRunning = true;
                document.getElementById('playIcon').className = 'fas fa-pause';
                document.getElementById('playText').textContent = 'إيقاف المراقبة';
                document.getElementById('toggleBtn').className = 'btn danger';
            }
        }

        function resetSimulation() {
            if (ecgSimulator) {
                ecgSimulator.reset();
            }
            isRunning = false;
            document.getElementById('playIcon').className = 'fas fa-play';
            document.getElementById('playText').textContent = 'بدء المراقبة';
            document.getElementById('toggleBtn').className = 'btn success';
        }

        function saveECG() {
            if (ecgData.length === 0) {
                alert('لا توجد بيانات للحفظ');
                return;
            }

            const csvContent = "data:text/csv;charset=utf-8,"
                + "Time,ECG_Value\n"
                + ecgData.map(point => `${point.time.toFixed(3)},${point.value.toFixed(6)}`).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `ecg_data_${new Date().toISOString().slice(0,19)}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function adjustSettings() {
            const heartRate = prompt('أدخل معدل القلب المطلوب (نبضة/دقيقة):', ecgSimulator ? ecgSimulator.heartRate : 75);
            if (heartRate && ecgSimulator) {
                ecgSimulator.heartRate = parseInt(heartRate);
                ecgSimulator.updateStatus(`تم تعديل معدل القلب إلى ${heartRate} نبضة/دقيقة`, 'info');
            }
        }

        function exportMeasurements() {
            const measurements = {
                heartRate: document.getElementById('heartRate').textContent,
                voltage: document.getElementById('voltage').textContent,
                rrInterval: document.getElementById('rrInterval').textContent,
                quality: document.getElementById('quality').textContent,
                prInterval: document.getElementById('prInterval').textContent,
                qtInterval: document.getElementById('qtInterval').textContent,
                timestamp: new Date().toISOString()
            };

            const jsonContent = "data:text/json;charset=utf-8," + JSON.stringify(measurements, null, 2);
            const encodedUri = encodeURI(jsonContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `ecg_measurements_${new Date().toISOString().slice(0,19)}.json`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Section Navigation
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionName + '-section').style.display = 'block';

            // Add active class to clicked tab
            event.target.classList.add('active');

            currentSection = sectionName;
        }

        // Circuit Display Functions
        function showCircuit(circuitType) {
            currentCircuit = circuitType;
            const display = document.getElementById('circuitDisplay');

            // Remove active class from all circuit buttons
            document.querySelectorAll('#circuits-section .btn').forEach(btn => {
                btn.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
            });

            // Add active style to clicked button
            document.getElementById(circuitType + 'Btn').style.background = 'linear-gradient(45deg, #5a6fd8, #6a42a0)';

            // Show circuit controls
            document.getElementById('zoomInBtn').style.display = 'inline-flex';
            document.getElementById('zoomOutBtn').style.display = 'inline-flex';
            document.getElementById('downloadBtn').style.display = 'inline-flex';

            let circuitContent = '';

            switch(circuitType) {
                case 'complete':
                    circuitContent = `
                        <h3 style="color: #667eea; margin-bottom: 20px;">الدائرة الكاملة لتسجيل ECG</h3>
                        <svg width="600" height="300" viewBox="0 0 600 300" style="background: white; border-radius: 10px;">
                            <!-- Power Supply -->
                            <rect x="50" y="20" width="60" height="30" fill="#ff6b6b" stroke="#333" stroke-width="2"/>
                            <text x="80" y="40" text-anchor="middle" font-size="12" fill="white">+5V</text>

                            <!-- Reference Circuit -->
                            <rect x="150" y="50" width="80" height="40" fill="#51cf66" stroke="#333" stroke-width="2"/>
                            <text x="190" y="75" text-anchor="middle" font-size="10">Vref Circuit</text>

                            <!-- AD623 Amplifier -->
                            <rect x="280" y="100" width="100" height="60" fill="#339af0" stroke="#333" stroke-width="2"/>
                            <text x="330" y="135" text-anchor="middle" font-size="12" fill="white">AD623</text>

                            <!-- Filters -->
                            <rect x="420" y="120" width="80" height="40" fill="#ffd43b" stroke="#333" stroke-width="2"/>
                            <text x="460" y="145" text-anchor="middle" font-size="10">Filters</text>

                            <!-- Connections -->
                            <line x1="110" y1="35" x2="150" y2="70" stroke="#333" stroke-width="2"/>
                            <line x1="230" y1="70" x2="280" y2="130" stroke="#333" stroke-width="2"/>
                            <line x1="380" y1="130" x2="420" y2="140" stroke="#333" stroke-width="2"/>

                            <!-- Input Electrodes -->
                            <circle cx="30" cy="130" r="15" fill="#e03131"/>
                            <text x="30" y="135" text-anchor="middle" font-size="10" fill="white">LA</text>
                            <circle cx="30" cy="160" r="15" fill="#e03131"/>
                            <text x="30" y="165" text-anchor="middle" font-size="10" fill="white">RA</text>
                            <circle cx="30" cy="190" r="15" fill="#228be6"/>
                            <text x="30" y="195" text-anchor="middle" font-size="10" fill="white">RL</text>

                            <!-- Input connections -->
                            <line x1="45" y1="130" x2="280" y2="120" stroke="#e03131" stroke-width="2"/>
                            <line x1="45" y1="160" x2="280" y2="140" stroke="#e03131" stroke-width="2"/>
                            <line x1="45" y1="190" x2="150" y2="90" stroke="#228be6" stroke-width="2"/>

                            <!-- Output -->
                            <rect x="520" y="125" width="60" height="30" fill="#37b24d" stroke="#333" stroke-width="2"/>
                            <text x="550" y="145" text-anchor="middle" font-size="10" fill="white">Output</text>
                            <line x1="500" y1="140" x2="520" y2="140" stroke="#333" stroke-width="2"/>
                        </svg>
                        <p style="margin-top: 15px; color: #666;">دائرة متكاملة تتضمن جميع المراحل: مصدر الطاقة، دائرة المرجع، مضخم الإشارة، والمرشحات</p>
                    `;
                    break;

                case 'amplifier':
                    circuitContent = `
                        <h3 style="color: #667eea; margin-bottom: 20px;">مضخم الإشارة AD623</h3>
                        <svg width="500" height="250" viewBox="0 0 500 250" style="background: white; border-radius: 10px;">
                            <!-- AD623 IC -->
                            <rect x="200" y="100" width="120" height="80" fill="#339af0" stroke="#333" stroke-width="2"/>
                            <text x="260" y="145" text-anchor="middle" font-size="14" fill="white">AD623</text>

                            <!-- Input pins -->
                            <circle cx="190" cy="120" r="3" fill="#333"/>
                            <text x="180" y="125" text-anchor="end" font-size="10">+IN</text>
                            <circle cx="190" cy="140" r="3" fill="#333"/>
                            <text x="180" y="145" text-anchor="end" font-size="10">-IN</text>

                            <!-- Gain resistor -->
                            <rect x="220" y="190" width="40" height="15" fill="#ffd43b" stroke="#333" stroke-width="1"/>
                            <text x="240" y="200" text-anchor="middle" font-size="8">Rg=1kΩ</text>
                            <line x1="220" y1="180" x2="220" y2="190" stroke="#333" stroke-width="1"/>
                            <line x1="260" y1="180" x2="260" y2="190" stroke="#333" stroke-width="1"/>
                            <text x="210" y="175" font-size="8">RG</text>
                            <text x="265" y="175" font-size="8">RG</text>

                            <!-- Output -->
                            <circle cx="330" cy="140" r="3" fill="#333"/>
                            <text x="340" y="145" font-size="10">OUT</text>

                            <!-- Reference -->
                            <circle cx="260" cy="190" r="3" fill="#333"/>
                            <text x="270" y="195" font-size="10">REF</text>

                            <!-- Power -->
                            <circle cx="230" cy="90" r="3" fill="#ff6b6b"/>
                            <text x="235" y="85" font-size="10">+Vs</text>
                            <circle cx="290" cy="90" r="3" fill="#228be6"/>
                            <text x="295" y="85" font-size="10">-Vs</text>

                            <!-- Input connections -->
                            <line x1="50" y1="120" x2="190" y2="120" stroke="#e03131" stroke-width="2"/>
                            <line x1="50" y1="140" x2="190" y2="140" stroke="#e03131" stroke-width="2"/>
                            <text x="40" y="125" font-size="12" fill="#e03131">LA</text>
                            <text x="40" y="145" font-size="12" fill="#e03131">RA</text>

                            <!-- Output connection -->
                            <line x1="330" y1="140" x2="450" y2="140" stroke="#37b24d" stroke-width="2"/>
                            <text x="460" y="145" font-size="12" fill="#37b24d">إلى المرشحات</text>

                            <!-- Reference connection -->
                            <line x1="260" y1="190" x2="260" y2="220" stroke="#ffd43b" stroke-width="2"/>
                            <text x="270" y="235" font-size="10" fill="#ffd43b">Vref = 2.5V</text>
                        </svg>
                        <p style="margin-top: 15px; color: #666;">مضخم أجهزة عالي الدقة مع كسب قابل للتعديل ومقاومة عالية للضوضاء المشتركة</p>
                    `;
                    break;

                case 'filter':
                    circuitContent = `
                        <h3 style="color: #667eea; margin-bottom: 20px;">دوائر المرشحات</h3>
                        <svg width="600" height="200" viewBox="0 0 600 200" style="background: white; border-radius: 10px;">
                            <!-- HPF -->
                            <rect x="50" y="80" width="80" height="40" fill="#ff8cc8" stroke="#333" stroke-width="2"/>
                            <text x="90" y="105" text-anchor="middle" font-size="10">HPF 0.5Hz</text>

                            <!-- LPF -->
                            <rect x="200" y="80" width="80" height="40" fill="#74c0fc" stroke="#333" stroke-width="2"/>
                            <text x="240" y="105" text-anchor="middle" font-size="10">LPF 150Hz</text>

                            <!-- Notch Filter -->
                            <rect x="350" y="80" width="80" height="40" fill="#ffd43b" stroke="#333" stroke-width="2"/>
                            <text x="390" y="105" text-anchor="middle" font-size="10">Notch 50Hz</text>

                            <!-- Connections -->
                            <line x1="130" y1="100" x2="200" y2="100" stroke="#333" stroke-width="2"/>
                            <line x1="280" y1="100" x2="350" y2="100" stroke="#333" stroke-width="2"/>
                            <line x1="430" y1="100" x2="500" y2="100" stroke="#333" stroke-width="2"/>

                            <!-- Input/Output labels -->
                            <text x="20" y="105" font-size="12">من المضخم</text>
                            <text x="510" y="105" font-size="12">إلى المخرج</text>

                            <!-- Component values -->
                            <text x="90" y="140" text-anchor="middle" font-size="8">C=1µF, R=330kΩ</text>
                            <text x="240" y="140" text-anchor="middle" font-size="8">R=10kΩ, C=0.1µF</text>
                            <text x="390" y="140" text-anchor="middle" font-size="8">Twin-T Network</text>
                        </svg>
                        <p style="margin-top: 15px; color: #666;">سلسلة مرشحات لإزالة الضوضاء والترددات غير المرغوبة من إشارة ECG</p>
                    `;
                    break;

                case 'reference':
                    circuitContent = `
                        <h3 style="color: #667eea; margin-bottom: 20px;">دائرة المرجع المستقر</h3>
                        <svg width="400" height="200" viewBox="0 0 400 200" style="background: white; border-radius: 10px;">
                            <!-- Voltage Divider -->
                            <rect x="100" y="50" width="15" height="40" fill="#ffd43b" stroke="#333" stroke-width="1"/>
                            <text x="125" y="75" font-size="8">R1=10kΩ</text>
                            <rect x="100" y="110" width="15" height="40" fill="#ffd43b" stroke="#333" stroke-width="1"/>
                            <text x="125" y="135" font-size="8">R2=10kΩ</text>

                            <!-- Op Amp Buffer -->
                            <polygon points="180,80 180,120 220,100" fill="#51cf66" stroke="#333" stroke-width="2"/>
                            <text x="200" y="105" text-anchor="middle" font-size="8">MCP6002</text>

                            <!-- Connections -->
                            <line x1="107" y1="30" x2="107" y2="50" stroke="#ff6b6b" stroke-width="2"/>
                            <line x1="107" y1="90" x2="107" y2="110" stroke="#333" stroke-width="2"/>
                            <line x1="107" y1="150" x2="107" y2="170" stroke="#228be6" stroke-width="2"/>
                            <line x1="107" y1="100" x2="180" y2="100" stroke="#333" stroke-width="2"/>
                            <line x1="220" y1="100" x2="280" y2="100" stroke="#37b24d" stroke-width="2"/>
                            <line x1="200" y1="120" x2="200" y2="140" stroke="#333" stroke-width="1"/>
                            <line x1="200" y1="140" x2="280" y2="140" stroke="#333" stroke-width="1"/>
                            <line x1="280" y1="140" x2="280" y2="100" stroke="#333" stroke-width="1"/>

                            <!-- Labels -->
                            <text x="107" y="20" text-anchor="middle" font-size="10" fill="#ff6b6b">+5V</text>
                            <text x="107" y="185" text-anchor="middle" font-size="10" fill="#228be6">GND</text>
                            <text x="290" y="105" font-size="10" fill="#37b24d">Vref = 2.5V</text>

                            <!-- Capacitor -->
                            <rect x="300" y="95" width="20" height="10" fill="none" stroke="#333" stroke-width="1"/>
                            <line x1="300" y1="105" x2="300" y2="120" stroke="#333" stroke-width="1"/>
                            <line x1="320" y1="105" x2="320" y2="120" stroke="#333" stroke-width="1"/>
                            <line x1="300" y1="120" x2="320" y2="120" stroke="#333" stroke-width="1"/>
                            <text x="310" y="135" text-anchor="middle" font-size="8">C=10µF</text>
                        </svg>
                        <p style="margin-top: 15px; color: #666;">دائرة توليد جهد مرجعي مستقر عند منتصف جهد التغذية (2.5V)</p>
                    `;
                    break;
            }

            display.innerHTML = circuitContent;
        }

        function zoomIn() {
            const svg = document.querySelector('#circuitDisplay svg');
            if (svg) {
                const currentWidth = svg.style.width || '100%';
                const newWidth = parseInt(currentWidth) * 1.2 + '%';
                svg.style.width = newWidth;
                svg.style.height = 'auto';
            }
        }

        function zoomOut() {
            const svg = document.querySelector('#circuitDisplay svg');
            if (svg) {
                const currentWidth = svg.style.width || '100%';
                const newWidth = Math.max(50, parseInt(currentWidth) * 0.8) + '%';
                svg.style.width = newWidth;
                svg.style.height = 'auto';
            }
        }

        function downloadCircuit() {
            if (!currentCircuit) return;

            const svg = document.querySelector('#circuitDisplay svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    const link = document.createElement('a');
                    link.download = `ecg_circuit_${currentCircuit}.png`;
                    link.href = canvas.toDataURL();
                    link.click();
                };

                img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
            }
        }

        // Analysis Functions
        function updateSpectrum() {
            if (ecgData.length < 100) {
                alert('بحاجة إلى المزيد من البيانات للتحليل الطيفي');
                return;
            }

            const canvas = document.getElementById('spectrumCanvas');
            const ctx = canvas.getContext('2d');

            // Clear canvas
            ctx.fillStyle = '#001100';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Simulate frequency spectrum
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let i = 0; i < canvas.width; i++) {
                const freq = (i / canvas.width) * 50; // 0-50 Hz
                let amplitude = 0;

                // Simulate ECG frequency content
                if (freq < 1) amplitude = Math.random() * 0.3; // Low frequency
                else if (freq < 10) amplitude = Math.random() * 0.8 + 0.2; // Main ECG content
                else if (freq < 30) amplitude = Math.random() * 0.4; // Higher harmonics
                else amplitude = Math.random() * 0.1; // Noise

                const y = canvas.height - (amplitude * canvas.height * 0.8);

                if (i === 0) ctx.moveTo(i, y);
                else ctx.lineTo(i, y);
            }
            ctx.stroke();

            // Add frequency labels
            ctx.fillStyle = '#00ff00';
            ctx.font = '12px Arial';
            ctx.fillText('0 Hz', 10, canvas.height - 10);
            ctx.fillText('25 Hz', canvas.width/2 - 15, canvas.height - 10);
            ctx.fillText('50 Hz', canvas.width - 40, canvas.height - 10);
        }

        function generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                measurements: {
                    heartRate: document.getElementById('heartRate').textContent,
                    voltage: document.getElementById('voltage').textContent,
                    rrInterval: document.getElementById('rrInterval').textContent,
                    quality: document.getElementById('quality').textContent
                },
                hrv: {
                    rmssd: document.getElementById('rmssd').textContent,
                    sdnn: document.getElementById('sdnn').textContent
                },
                analysis: {
                    avgRR: document.getElementById('avgRR').textContent,
                    minRR: document.getElementById('minRR').textContent,
                    maxRR: document.getElementById('maxRR').textContent
                }
            };

            const reportContent = `
                تقرير تحليل ECG
                ================

                التاريخ والوقت: ${new Date().toLocaleString('ar-SA')}

                القياسات الأساسية:
                - معدل القلب: ${report.measurements.heartRate} نبضة/دقيقة
                - الجهد: ${report.measurements.voltage} ميلي فولت
                - فترة R-R: ${report.measurements.rrInterval} مللي ثانية
                - جودة الإشارة: ${report.measurements.quality}%

                تحليل تغيرات معدل القلب (HRV):
                - RMSSD: ${report.hrv.rmssd} مللي ثانية
                - SDNN: ${report.hrv.sdnn} مللي ثانية

                الإحصائيات:
                - متوسط R-R: ${report.analysis.avgRR} مللي ثانية
                - أدنى R-R: ${report.analysis.minRR} مللي ثانية
                - أعلى R-R: ${report.analysis.maxRR} مللي ثانية

                التوصيات:
                - الإيقاع ضمن النطاق الطبيعي
                - يُنصح بمراجعة طبيب مختص للتفسير الطبي الدقيق
            `;

            const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `ecg_report_${new Date().toISOString().slice(0,19)}.txt`;
            link.click();
        }

        function exportData() {
            if (ecgData.length === 0) {
                alert('لا توجد بيانات للتصدير');
                return;
            }

            const data = {
                ecgData: ecgData,
                rrIntervals: rrIntervals,
                measurements: {
                    heartRate: document.getElementById('heartRate').textContent,
                    voltage: document.getElementById('voltage').textContent,
                    quality: document.getElementById('quality').textContent
                },
                timestamp: new Date().toISOString()
            };

            const jsonContent = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonContent], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `ecg_data_${new Date().toISOString().slice(0,19)}.json`;
            link.click();
        }

        // Educational Functions
        function showEducationalContent(topic) {
            const contentDiv = document.getElementById('educationalContent');
            contentDiv.style.display = 'block';

            let content = '';

            switch(topic) {
                case 'physiology':
                    content = `
                        <h3 style="color: #667eea; margin-bottom: 20px;">فسيولوجيا القلب والإشارات الكهربائية</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <h4>كيف يعمل القلب:</h4>
                                <ul style="margin: 10px 0; padding-right: 20px;">
                                    <li>القلب عضلة تضخ الدم عبر الجسم</li>
                                    <li>يحتوي على 4 حجرات: أذينان وبطينان</li>
                                    <li>ينقبض ويسترخي في دورة منتظمة</li>
                                    <li>يولد إشارات كهربائية تنظم النبضات</li>
                                </ul>
                            </div>
                            <div>
                                <h4>الإشارات الكهربائية:</h4>
                                <ul style="margin: 10px 0; padding-right: 20px;">
                                    <li>تبدأ من العقدة الجيبية الأذينية</li>
                                    <li>تنتشر عبر الأذينين ثم البطينين</li>
                                    <li>تسبب انقباض العضلة القلبية</li>
                                    <li>يمكن قياسها من سطح الجلد</li>
                                </ul>
                            </div>
                        </div>
                        <button class="btn" onclick="nextEducationalStep('physiology')" style="margin-top: 20px;">
                            <i class="fas fa-arrow-left"></i> التالي: دورة القلب
                        </button>
                    `;
                    break;

                case 'waves':
                    content = `
                        <h3 style="color: #10b981; margin-bottom: 20px;">مكونات موجة ECG</h3>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                                <div style="text-align: center;">
                                    <div style="width: 60px; height: 40px; background: #ff6b6b; margin: 0 auto 10px; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">P</div>
                                    <h4>موجة P</h4>
                                    <p style="font-size: 0.9rem;">انقباض الأذينين<br>المدة: 80-120 مللي ثانية</p>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 60px; height: 40px; background: #51cf66; margin: 0 auto 10px; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">QRS</div>
                                    <h4>مركب QRS</h4>
                                    <p style="font-size: 0.9rem;">انقباض البطينين<br>المدة: 60-100 مللي ثانية</p>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 60px; height: 40px; background: #339af0; margin: 0 auto 10px; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">T</div>
                                    <h4>موجة T</h4>
                                    <p style="font-size: 0.9rem;">استرخاء البطينين<br>المدة: 160 مللي ثانية</p>
                                </div>
                            </div>
                        </div>
                        <button class="btn" onclick="nextEducationalStep('waves')" style="margin-top: 20px;">
                            <i class="fas fa-arrow-left"></i> التالي: الفترات الزمنية
                        </button>
                    `;
                    break;

                case 'circuits':
                    content = `
                        <h3 style="color: #f59e0b; margin-bottom: 20px;">تصميم دوائر ECG</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <h4>المتطلبات الأساسية:</h4>
                                <ul style="margin: 10px 0; padding-right: 20px;">
                                    <li>مضخم تفاضلي عالي الكسب</li>
                                    <li>مقاومة دخل عالية (>10MΩ)</li>
                                    <li>مقاومة ضوضاء مشتركة عالية</li>
                                    <li>مرشحات لإزالة الضوضاء</li>
                                </ul>
                            </div>
                            <div>
                                <h4>مراحل الدائرة:</h4>
                                <ul style="margin: 10px 0; padding-right: 20px;">
                                    <li>مرحلة التضخيم (AD623)</li>
                                    <li>دائرة المرجع (Vcc/2)</li>
                                    <li>مرشح عالي التمرير (0.5Hz)</li>
                                    <li>مرشح منخفض التمرير (150Hz)</li>
                                </ul>
                            </div>
                        </div>
                        <button class="btn" onclick="nextEducationalStep('circuits')" style="margin-top: 20px;">
                            <i class="fas fa-arrow-left"></i> التالي: حسابات التصميم
                        </button>
                    `;
                    break;

                case 'medical':
                    content = `
                        <h3 style="color: #3b82f6; margin-bottom: 20px;">التطبيقات الطبية لـ ECG</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                            <div style="background: #f0f9ff; padding: 15px; border-radius: 10px;">
                                <h4 style="color: #3b82f6;">التشخيص:</h4>
                                <ul style="margin: 10px 0; padding-right: 20px; font-size: 0.9rem;">
                                    <li>اضطرابات النظم القلبية</li>
                                    <li>احتشاء عضلة القلب</li>
                                    <li>تضخم القلب</li>
                                    <li>اضطرابات التوصيل</li>
                                </ul>
                            </div>
                            <div style="background: #f0fdf4; padding: 15px; border-radius: 10px;">
                                <h4 style="color: #10b981;">المراقبة:</h4>
                                <ul style="margin: 10px 0; padding-right: 20px; font-size: 0.9rem;">
                                    <li>مراقبة المرضى في العناية المركزة</li>
                                    <li>فحص اللياقة البدنية</li>
                                    <li>مراقبة الأدوية القلبية</li>
                                    <li>الفحص الدوري للقلب</li>
                                </ul>
                            </div>
                        </div>
                        <button class="btn" onclick="nextEducationalStep('medical')" style="margin-top: 20px;">
                            <i class="fas fa-arrow-left"></i> التالي: معايير التفسير
                        </button>
                    `;
                    break;
            }

            contentDiv.innerHTML = content;
        }

        function nextEducationalStep(topic) {
            // Implement next step functionality
            alert('سيتم إضافة المزيد من المحتوى التعليمي قريباً');
        }

        function startQuiz() {
            const quizContainer = document.getElementById('quizContainer');
            quizContainer.innerHTML = `
                <div style="text-align: right;">
                    <h3 style="margin-bottom: 20px;">السؤال 1 من 5</h3>
                    <p style="font-size: 1.1rem; margin-bottom: 20px;">ما هي المدة الطبيعية لمركب QRS؟</p>
                    <div style="display: grid; gap: 10px;">
                        <button class="btn" onclick="selectAnswer(1, 'wrong')" style="width: 100%;">40-60 مللي ثانية</button>
                        <button class="btn" onclick="selectAnswer(1, 'correct')" style="width: 100%;">60-100 مللي ثانية</button>
                        <button class="btn" onclick="selectAnswer(1, 'wrong')" style="width: 100%;">120-200 مللي ثانية</button>
                        <button class="btn" onclick="selectAnswer(1, 'wrong')" style="width: 100%;">200-300 مللي ثانية</button>
                    </div>
                </div>
            `;
        }

        function selectAnswer(questionNum, result) {
            if (result === 'correct') {
                alert('إجابة صحيحة! 🎉');
            } else {
                alert('إجابة خاطئة. حاول مرة أخرى.');
            }
        }

        // Initialize Application
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize ECG simulator
            setTimeout(() => {
                ecgSimulator = new ECGSimulator();
                document.getElementById('status').innerHTML = '<i class="fas fa-circle pulse" style="color: #28a745;"></i> جاهز للبدء';
            }, 1000);

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.key === ' ') { // Spacebar to toggle
                    e.preventDefault();
                    toggleSimulation();
                } else if (e.key === 'r' || e.key === 'R') { // R to reset
                    resetSimulation();
                } else if (e.key === 's' || e.key === 'S') { // S to save
                    saveECG();
                }
            });

            // Add window resize handler
            window.addEventListener('resize', function() {
                if (ecgSimulator) {
                    ecgSimulator.setupCanvas();
                }
            });
        });
    </script>
</body>
</html>

                <!-- Diagram 1-3 -->
                <div class="diagram-container">
                    <h2 class="text-xl font-semibold text-blue-700 mb-4">شكل 1-3: مرحلة التضخيم التفاضلي باستخدام AD623</h2>
                    <div class="bg-white p-4 rounded border border-gray-200 flex justify-center">
                        <img src="https://via.placeholder.com/400x300?text=AD623+Amplifier" alt="AD623 Amplifier" class="max-w-full h-auto">
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">رسم تخطيطي يركز على مضخم الأجهزة AD623 (U1). يجب أن يوضح توصيل قطبي الدخل LA و RA بالطرفين +IN و -IN، توصيل المقاومة Rg بين طرفي RG، توصيل طرف REF بجهد المرجع Vcc/2، توصيل أطراف التغذية (+Vs, -Vs) بـ Vcc و GND، وإظهار طرف المخرج OUTPUT.</p>
                        <div class="search-terms">
                            <h4 class="font-medium text-green-800 mb-1">مصطلحات بحث مقترحة:</h4>
                            <p class="text-sm text-gray-700">"AD623 instrumentation amplifier circuit diagram", "ECG instrumentation amplifier stage schematic", "AD623 gain setting resistor connection", "Differential amplifier ECG AD623"</p>
                        </div>
                    </div>
                </div>

                <!-- Diagram 1-8 -->
                <div class="diagram-container">
                    <h2 class="text-xl font-semibold text-blue-700 mb-4">شكل 1-8: مواضع الأقطاب لتسجيل ECG Lead I</h2>
                    <div class="bg-white p-4 rounded border border-gray-200 flex justify-center">
                        <img src="https://via.placeholder.com/400x300?text=Electrode+Placement" alt="Electrode Placement" class="max-w-full h-auto">
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">رسم تخطيطي بسيط لجسم الإنسان (الجذع والأطراف) يوضح موقع وضع القطب على الذراع الأيمن (RA)، الذراع الأيسر (LA)، والساق اليمنى (RL) لتكوين Lead I القياسي. يمكن الإشارة إلى مثلث أينهوفن بشكل مبسط.</p>
                        <div class="search-terms">
                            <h4 class="font-medium text-green-800 mb-1">مصطلحات بحث مقترحة:</h4>
                            <p class="text-sm text-gray-700">"ECG Lead I electrode placement diagram", "Standard limb leads placement ECG", "Einthoven triangle Lead I"</p>
                        </div>
                    </div>
                </div>

                <!-- Diagram 1-9 -->
                <div class="diagram-container">
                    <h2 class="text-xl font-semibold text-blue-700 mb-4">شكل 1-9: أشكال الموجات المتوقعة لإشارة ECG</h2>
                    <div class="waveform">
                        <div class="waveform-line"></div>
                        <svg class="ecg-wave" viewBox="0 0 1000 200" preserveAspectRatio="none">
                            <!-- ECG Waveform -->
                            <path d="M0,100 L50,100 L60,80 L70,120 L80,100 L150,100 L160,60 L170,140 L180,100 L250,100 
                                    L260,80 L270,120 L280,100 L350,100 L360,60 L370,140 L380,100 L450,100 
                                    L460,80 L470,120 L480,100 L550,100 L560,60 L570,140 L580,100 L650,100 
                                    L660,80 L670,120 L680,100 L750,100 L760,60 L770,140 L780,100 L850,100 
                                    L860,80 L870,120 L880,100 L950,100 L960,60 L970,140 L980,100 L1000,100" 
                                    stroke="#3b82f6" stroke-width="2" fill="none" />
                        </svg>
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">رسم بياني يمثل دورة قلبية واحدة نموذجية (أو دورتين). المحور الأفقي يمثل الزمن (بالمللي ثانية أو الثانية)، والمحور الرأسي يمثل الجهد (بالميلي فولت). يجب تحديد الموجات P, QRS complex, T بوضوح، بالإضافة إلى خط الأساس (Isoelectric line) وفترات مهمة مثل PR interval و QT interval.</p>
                        <div class="search-terms">
                            <h4 class="font-medium text-green-800 mb-1">مصطلحات بحث مقترحة:</h4>
                            <p class="text-sm text-gray-700">"ECG waveform P QRS T diagram", "Normal sinus rhythm ECG components", "ECG wave morphology", "Single ECG cycle graph"</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Tables and Info -->
            <div>
                <!-- Components Table -->
                <div class="diagram-container">
                    <h2 class="text-xl font-semibold text-blue-700 mb-4">جدول 1-1: قائمة المكونات والمواصفات لتجربة ECG</h2>
                    <div class="overflow-x-auto">
                        <table class="component-table">
                            <thead>
                                <tr>
                                    <th>الرمز في الدائرة</th>
                                    <th>نوع المكون</th>
                                    <th>القيمة/رقم القطعة</th>
                                    <th>مواصفات هامة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>U1</td>
                                    <td>مضخم أجهزة</td>
                                    <td>AD623AN</td>
                                    <td>DIP-8, Gain 100</td>
                                </tr>
                                <tr>
                                    <td>U2A</td>
                                    <td>مضخم عمليات</td>
                                    <td>MCP6002</td>
                                    <td>DIP-8, Single Supply</td>
                                </tr>
                                <tr>
                                    <td>R1, R2</td>
                                    <td>مقاومة</td>
                                    <td>10kΩ</td>
                                    <td>1%, 1/4W</td>
                                </tr>
                                <tr>
                                    <td>Rg</td>
                                    <td>مقاومة</td>
                                    <td>1kΩ</td>
                                    <td>1%, 1/4W</td>
                                </tr>
                                <tr>
                                    <td>R_hpf</td>
                                    <td>مقاومة</td>
                                    <td>330kΩ</td>
                                    <td>1%, 1/4W</td>
                                </tr>
                                <tr>
                                    <td>C_hpf</td>
                                    <td>مكثف</td>
                                    <td>1µF</td>
                                    <td>16V, Ceramic</td>
                                </tr>
                                <tr>
                                    <td>R_lpf</td>
                                    <td>مقاومة</td>
                                    <td>10kΩ</td>
                                    <td>1%, 1/4W</td>
                                </tr>
                                <tr>
                                    <td>C_lpf</td>
                                    <td>مكثف</td>
                                    <td>0.1µF</td>
                                    <td>16V, Ceramic</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">جدول يسرد جميع المكونات الإلكترونية المستخدمة في الدائرة.</p>
                        <div class="search-terms">
                            <h4 class="font-medium text-green-800 mb-1">مصطلحات بحث مقترحة:</h4>
                            <p class="text-sm text-gray-700">"Bill of Materials template electronics", "Components list table format", "ECG circuit parts list"</p>
                        </div>
                    </div>
                </div>

                <!-- Resistors/Capacitors Table -->
                <div class="diagram-container">
                    <h2 class="text-xl font-semibold text-blue-700 mb-4">جدول 1-2: قيم المقاومات والمكثفات المستخدمة في دائرة ECG</h2>
                    <div class="overflow-x-auto">
                        <table class="component-table">
                            <thead>
                                <tr>
                                    <th>المكون</th>
                                    <th>القيمة الاسمية</th>
                                    <th>القيمة المقاسة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Rg</td>
                                    <td>1kΩ</td>
                                    <td>0.98kΩ</td>
                                </tr>
                                <tr>
                                    <td>R_hpf</td>
                                    <td>330kΩ</td>
                                    <td>328kΩ</td>
                                </tr>
                                <tr>
                                    <td>C_hpf</td>
                                    <td>1µF</td>
                                    <td>0.97µF</td>
                                </tr>
                                <tr>
                                    <td>R_lpf</td>
                                    <td>10kΩ</td>
                                    <td>9.92kΩ</td>
                                </tr>
                                <tr>
                                    <td>C_lpf</td>
                                    <td>0.1µF</td>
                                    <td>0.098µF</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">جدول يركز على قيم المكونات السلبية المستخدمة في تحديد الكسب وترددات القطع.</p>
                    </div>
                </div>

                <!-- Results Table -->
                <div class="diagram-container">
                    <h2 class="text-xl font-semibold text-blue-700 mb-4">جدول 1-3: النتائج المقاسة والحسابات لتجربة ECG</h2>
                    <div class="overflow-x-auto">
                        <table class="component-table">
                            <thead>
                                <tr>
                                    <th>المعلمة المقاسة</th>
                                    <th>القيمة</th>
                                    <th>الوحدة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Vcc</td>
                                    <td>5.02</td>
                                    <td>V</td>
                                </tr>
                                <tr>
                                    <td>Vref</td>
                                    <td>2.51</td>
                                    <td>V</td>
                                </tr>
                                <tr>
                                    <td>G_measured</td>
                                    <td>98.5</td>
                                    <td>-</td>
                                </tr>
                                <tr>
                                    <td>R-R interval</td>
                                    <td>800</td>
                                    <td>ms</td>
                                </tr>
                                <tr>
                                    <td>HR</td>
                                    <td>75</td>
                                    <td>bpm</td>
                                </tr>
                                <tr>
                                    <td>QRS amplitude</td>
                                    <td>1.2</td>
                                    <td>mV</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">جدول لتسجيل البيانات المجمعة من الدائرة والإشارة الحيوية.</p>
                        <div class="search-terms">
                            <h4 class="font-medium text-green-800 mb-1">مصطلحات بحث مقترحة:</h4>
                            <p class="text-sm text-gray-700">"ECG experiment results table", "ECG lab data sheet template", "Heart rate calculation table ECG"</p>
                        </div>
                    </div>
                </div>

                <!-- IC Pinouts -->
                <div class="diagram-container">
                    <h2 class="text-xl font-semibold text-blue-700 mb-4">مخططات توزيع الأرجل</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h3 class="font-medium text-gray-700 mb-2">AD623 (DIP-8)</h3>
                            <div class="bg-white p-4 rounded border border-gray-200 flex justify-center">
                                <img src="https://via.placeholder.com/200x150?text=AD623+Pinout" alt="AD623 Pinout" class="max-w-full h-auto">
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-700 mb-2">MCP6002 (DIP-8)</h3>
                            <div class="bg-white p-4 rounded border border-gray-200 flex justify-center">
                                <img src="https://via.placeholder.com/200x150?text=MCP6002+Pinout" alt="MCP6002 Pinout" class="max-w-full h-auto">
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-700">رسم قياسي يوضح شكل الدائرة المتكاملة (IC) للحزمة المستخدمة مع ترقيم الأرجل وتسمية وظيفة كل رجل.</p>
                    </div>
                </div>

                <!-- Additional Resources -->
                <div class="diagram-container">
                    <h2 class="text-xl font-semibold text-blue-700 mb-4">موارد إضافية</h2>
                    <div class="space-y-3">
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-file-pdf mr-2"></i> دليل المستخدم لـ AD623
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-file-pdf mr-2"></i> ورقة بيانات MCP6002
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-link mr-2"></i> موقع مشروع ECG مفتوح المصدر
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-video mr-2"></i> فيديو تعليمي لتركيب الأقطاب
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-12 pt-6 border-t border-gray-200 text-center text-gray-600 text-sm">
            <p>© 2023 معمل الإشارات الحيوية - كلية الهندسة</p>
            <p class="mt-1">تم التطوير باستخدام HTML, CSS و JavaScript</p>
        </footer>
    </div>
</body>
</html>