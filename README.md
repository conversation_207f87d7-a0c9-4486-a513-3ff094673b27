# مختبر تخطيط كهربية القلب (ECG Lab)

## نظام متقدم لقياس وتحليل الإشارات الكهروفسيولوجية

### نظرة عامة
هذا المشروع عبارة عن تطبيق ويب تفاعلي لمحاكاة وتحليل إشارات تخطيط كهربية القلب (ECG). يوفر النظام واجهة شاملة لعرض القياسات المباشرة، الدوائر الإلكترونية، وتحليل الإشارات الحيوية.

### الميزات الرئيسية

#### 🫀 محاكاة ECG المباشرة
- موجات ECG متحركة وواقعية
- قياسات مباشرة لمعدل ضربات القلب
- عرض جهد QRS وفترة R-R
- مؤشرات جودة الإشارة

#### 🔧 الدوائر الإلكترونية التفاعلية
- رسوم تخطيطية تفاعلية للدوائر
- مضخم الأجهزة AD623
- دوائر المرشحات (HPF/LPF)
- دائرة المرجع المستقر

#### 📊 تحليل الإشارات
- تحليل الطيف الترددي
- تحليل HRV (Heart Rate Variability)
- إحصائيات القياس المفصلة
- رسوم Poincaré للتحليل

#### 📱 تصميم متجاوب
- يعمل على جميع الأجهزة
- واجهة باللغة العربية
- تحكم بالإيماءات واللمس
- تحسين للهواتف المحمولة

### هيكل المشروع

```
ECG-Lab/
├── css/
│   ├── styles.css          # الأنماط الرئيسية
│   └── animations.css      # الحركات والتأثيرات
├── js/
│   ├── ecg-simulation.js   # محاكاة إشارات ECG
│   ├── circuit-diagrams.js # الدوائر التفاعلية
│   ├── measurements.js     # نظام القياسات
│   └── main.js            # التحكم الرئيسي
├── ecg-lab.html           # الصفحة الرئيسية
└── README.md              # هذا الملف
```

### التقنيات المستخدمة

- **HTML5**: هيكل الصفحة والعناصر التفاعلية
- **CSS3**: التصميم والحركات المتقدمة
- **JavaScript ES6+**: المنطق والتفاعل
- **Canvas API**: رسم الموجات والرسوم البيانية
- **SVG**: الدوائر الإلكترونية التفاعلية
- **Web APIs**: التحكم في الملفات والتصدير

### كيفية الاستخدام

1. **فتح التطبيق**
   ```bash
   # افتح الملف في المتصفح
   open ecg-lab.html
   ```

2. **التنقل**
   - استخدم القائمة العلوية للتنقل بين الأقسام
   - انقر على "بدء القياس" لبدء المحاكاة
   - استخدم "عرض الدوائر" للانتقال لقسم الدوائر

3. **التحكم في القياسات**
   - زر الإيقاف/التشغيل للموجة
   - زر إعادة التعيين لبدء جديد
   - زر الحفظ لتصدير البيانات

4. **استكشاف الدوائر**
   - انقر على التبويبات لعرض دوائر مختلفة
   - مرر الماوس فوق المكونات لمعلومات إضافية
   - استخدم أزرار التكبير/التصغير

### المكونات الإلكترونية

#### مضخم الأجهزة (AD623)
- **الكسب**: 100 (قابل للتعديل بـ Rg)
- **مقاومة الكسب**: 1kΩ
- **جهد المرجع**: Vcc/2

#### المرشحات
- **HPF**: fc = 0.48 Hz (R=330kΩ, C=1µF)
- **LPF**: fc = 159 Hz (R=10kΩ, C=0.1µF)

#### دائرة المرجع
- **مقسم الجهد**: R1=R2=10kΩ
- **المخزن المؤقت**: MCP6002
- **الخرج**: Vref = Vcc/2 = 2.5V

### الاختصارات

- **Ctrl+S**: حفظ الموجة الحالية
- **Ctrl+R**: إعادة تعيين القياسات
- **Ctrl+E**: تصدير البيانات
- **Space**: إيقاف/تشغيل المحاكاة

### التخصيص

#### تعديل معاملات ECG
```javascript
// في ملف ecg-simulation.js
this.heartRate = 75;        // معدل القلب (BPM)
this.amplitude = 1.2;       // سعة الموجة (mV)
this.noiseLevel = 0.05;     // مستوى الضوضاء
```

#### إضافة مكونات جديدة
```javascript
// في ملف circuit-diagrams.js
const newComponent = {
    id: 'R3',
    type: 'resistor',
    name: '22kΩ',
    description: 'مقاومة إضافية',
    x: 300,
    y: 200
};
```

### المتطلبات

- متصفح حديث يدعم HTML5 و Canvas
- JavaScript مفعل
- اتصال بالإنترنت (للخطوط والأيقونات)

### الدعم والمساهمة

هذا المشروع مفتوح المصدر ومرحب بالمساهمات:

1. **الإبلاغ عن الأخطاء**: استخدم نظام Issues
2. **اقتراح ميزات جديدة**: شارك أفكارك
3. **المساهمة في الكود**: أرسل Pull Requests
4. **تحسين الترجمة**: ساعد في الترجمة

### الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

### الشكر والتقدير

- **Analog Devices**: لمضخم الأجهزة AD623
- **Microchip**: لمضخم العمليات MCP6002
- **Font Awesome**: للأيقونات المستخدمة
- **Google Fonts**: لخط Tajawal العربي

### معلومات الاتصال

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: [ECG Lab](https://ecglab.edu)
- **الدعم الفني**: <EMAIL>

---

© 2024 مختبر الإشارات الحيوية - جميع الحقوق محفوظة
