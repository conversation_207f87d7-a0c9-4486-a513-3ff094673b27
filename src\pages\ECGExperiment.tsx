import React, { useState } from 'react';
import { Heart, Zap, Settings, Eye, FileText, AlertCircle } from 'lucide-react';
import CircuitDiagram from '../components/CircuitDiagram';
import ComponentTable from '../components/ComponentTable';
import WaveformDisplay from '../components/WaveformDisplay';

const ECGExperiment = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Eye },
    { id: 'circuit', label: 'Circuit Design', icon: Zap },
    { id: 'components', label: 'Components', icon: Settings },
    { id: 'waveforms', label: 'Waveforms', icon: Heart },
    { id: 'results', label: 'Results', icon: FileText }
  ];

  const components = [
    { reference: 'U1', type: 'Instrumentation Amplifier', value: 'AD623AN', specs: 'DIP-8, Low Noise, High CMRR' },
    { reference: 'U2A', type: 'Op-Amp', value: 'MCP6002', specs: 'Rail-to-Rail, Low Power' },
    { reference: 'Rg', type: 'Resistor', value: '100Ω', specs: '1%, Metal Film' },
    { reference: 'R1, R2', type: 'Resistor', value: '10kΩ', specs: '1%, Voltage Divider' },
    { reference: 'R_hpf', type: 'Resistor', value: '1MΩ', specs: '1%, High Pass Filter' },
    { reference: 'R_lpf', type: 'Resistor', value: '4kΩ', specs: '1%, Low Pass Filter' },
    { reference: 'C_hpf', type: 'Capacitor', value: '0.33μF', specs: 'Low Leakage, Polyester' },
    { reference: 'C_lpf', type: 'Capacitor', value: '1μF', specs: 'Low Leakage, Ceramic' },
    { reference: 'C_bias', type: 'Capacitor', value: '10μF', specs: 'Tantalum, 16V' }
  ];

  const specifications = [
    { parameter: 'Frequency Range', value: '0.5 - 40 Hz', unit: 'Hz' },
    { parameter: 'Amplitude Range', value: '0.1 - 3', unit: 'mV' },
    { parameter: 'Gain (Rg = 100Ω)', value: '1000', unit: 'V/V' },
    { parameter: 'Input Impedance', value: '> 10', unit: 'GΩ' },
    { parameter: 'CMRR', value: '> 100', unit: 'dB' },
    { parameter: 'Supply Voltage', value: '5', unit: 'V' }
  ];

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div className="flex items-center space-x-4 mb-6">
          <div className="p-3 bg-red-100 rounded-xl">
            <Heart className="h-8 w-8 text-red-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">ECG - Electrocardiogram</h1>
            <p className="text-lg text-gray-600">Heart electrical activity recording circuit</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-red-50 p-4 rounded-lg">
            <h3 className="font-semibold text-red-800 mb-2">Signal Characteristics</h3>
            <p className="text-sm text-red-700">P-QRS-T complex, 60-100 bpm</p>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">Lead Configuration</h3>
            <p className="text-sm text-blue-700">Lead I: LA(+), RA(-), RL(ref)</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-800 mb-2">Applications</h3>
            <p className="text-sm text-green-700">Cardiac monitoring, arrhythmia detection</p>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-xl shadow-lg mb-8">
        <div className="flex flex-wrap border-b border-gray-200">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-4 font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="space-y-8">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Experiment Overview</h2>
              <div className="space-y-4">
                <p className="text-gray-600">
                  This experiment demonstrates the design and implementation of a single-channel ECG recording system 
                  using Lead I configuration. The circuit amplifies the small electrical signals generated by the heart 
                  and filters them to produce a clean ECG waveform.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertCircle className="h-5 w-5 text-yellow-600" />
                    <span className="font-semibold text-yellow-800">Safety Notice</span>
                  </div>
                  <p className="text-sm text-yellow-700">
                    This circuit is for educational purposes only. Proper isolation and safety measures must be 
                    implemented for any human subject testing.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Technical Specifications</h2>
              <div className="space-y-3">
                {specifications.map((spec, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">{spec.parameter}</span>
                    <span className="font-semibold text-gray-900">{spec.value} {spec.unit}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'circuit' && (
          <div className="space-y-8">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Complete Circuit Schematic</h2>
              <CircuitDiagram 
                type="ecg" 
                title="ECG Lead I Recording Circuit"
                description="Complete circuit showing instrumentation amplifier, filters, and reference voltage"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Circuit Stages</h3>
                <div className="space-y-4">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-semibold text-gray-900">1. Instrumentation Amplifier (AD623)</h4>
                    <p className="text-sm text-gray-600">High CMRR differential amplifier with gain set by Rg</p>
                  </div>
                  <div className="border-l-4 border-green-500 pl-4">
                    <h4 className="font-semibold text-gray-900">2. High-Pass Filter (0.5 Hz)</h4>
                    <p className="text-sm text-gray-600">Removes DC offset and baseline drift</p>
                  </div>
                  <div className="border-l-4 border-orange-500 pl-4">
                    <h4 className="font-semibold text-gray-900">3. Low-Pass Filter (40 Hz)</h4>
                    <p className="text-sm text-gray-600">Reduces high-frequency noise and artifacts</p>
                  </div>
                  <div className="border-l-4 border-purple-500 pl-4">
                    <h4 className="font-semibold text-gray-900">4. Reference Voltage (Vcc/2)</h4>
                    <p className="text-sm text-gray-600">Provides stable mid-supply reference</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Design Calculations</h3>
                <div className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm font-medium text-gray-700">Gain Calculation:</p>
                    <p className="text-sm text-gray-600">G = 1 + (49.4kΩ / Rg)</p>
                    <p className="text-sm text-gray-600">G = 1 + (49.4kΩ / 100Ω) = 495 ≈ 500</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm font-medium text-gray-700">HPF Cutoff Frequency:</p>
                    <p className="text-sm text-gray-600">fc = 1 / (2π × R_hpf × C_hpf)</p>
                    <p className="text-sm text-gray-600">fc = 1 / (2π × 1MΩ × 0.33μF) = 0.48 Hz</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm font-medium text-gray-700">LPF Cutoff Frequency:</p>
                    <p className="text-sm text-gray-600">fc = 1 / (2π × R_lpf × C_lpf)</p>
                    <p className="text-sm text-gray-600">fc = 1 / (2π × 4kΩ × 1μF) = 39.8 Hz</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'components' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Component List & Specifications</h2>
            <ComponentTable components={components} />
          </div>
        )}

        {activeTab === 'waveforms' && (
          <div className="space-y-8">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Expected ECG Waveforms</h2>
              <WaveformDisplay 
                type="ecg" 
                title="Normal Sinus Rhythm"
                description="Typical ECG waveform showing P-QRS-T complex"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Electrode Placement</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                    <span className="text-gray-700"><strong>LA (Left Arm):</strong> Positive electrode</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700"><strong>RA (Right Arm):</strong> Negative electrode</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                    <span className="text-gray-700"><strong>RL (Right Leg):</strong> Reference electrode</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Waveform Components</h3>
                <div className="space-y-3">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-semibold text-gray-900">P Wave</h4>
                    <p className="text-sm text-gray-600">Atrial depolarization, 0.08-0.12s duration</p>
                  </div>
                  <div className="border-l-4 border-red-500 pl-4">
                    <h4 className="font-semibold text-gray-900">QRS Complex</h4>
                    <p className="text-sm text-gray-600">Ventricular depolarization, 0.06-0.10s duration</p>
                  </div>
                  <div className="border-l-4 border-green-500 pl-4">
                    <h4 className="font-semibold text-gray-900">T Wave</h4>
                    <p className="text-sm text-gray-600">Ventricular repolarization, 0.16-0.20s duration</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'results' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Measurement Results & Analysis</h2>
            <div className="space-y-6">
              <div className="overflow-x-auto">
                <table className="w-full table-auto">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Parameter</th>
                      <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Measured Value</th>
                      <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Expected Range</th>
                      <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Notes</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900">Supply Voltage (Vcc)</td>
                      <td className="px-4 py-3 text-sm text-gray-600">5.0 V</td>
                      <td className="px-4 py-3 text-sm text-gray-600">4.5 - 5.5 V</td>
                      <td className="px-4 py-3 text-sm text-gray-600">Within specifications</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900">Reference Voltage (Vref)</td>
                      <td className="px-4 py-3 text-sm text-gray-600">2.5 V</td>
                      <td className="px-4 py-3 text-sm text-gray-600">2.4 - 2.6 V</td>
                      <td className="px-4 py-3 text-sm text-gray-600">Stable reference</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900">Heart Rate</td>
                      <td className="px-4 py-3 text-sm text-gray-600">72 bpm</td>
                      <td className="px-4 py-3 text-sm text-gray-600">60 - 100 bpm</td>
                      <td className="px-4 py-3 text-sm text-gray-600">Normal sinus rhythm</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900">R-R Interval</td>
                      <td className="px-4 py-3 text-sm text-gray-600">833 ms</td>
                      <td className="px-4 py-3 text-sm text-gray-600">600 - 1000 ms</td>
                      <td className="px-4 py-3 text-sm text-gray-600">Consistent timing</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900">QRS Amplitude</td>
                      <td className="px-4 py-3 text-sm text-gray-600">1.2 mV</td>
                      <td className="px-4 py-3 text-sm text-gray-600">0.5 - 2.0 mV</td>
                      <td className="px-4 py-3 text-sm text-gray-600">Good signal quality</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ECGExperiment;