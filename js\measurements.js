// Measurements and Analysis Module
class MeasurementSystem {
    constructor() {
        this.measurements = {
            heartRate: 75,
            qrsVoltage: 1.2,
            rrInterval: 800,
            signalQuality: 95,
            pWaveAmplitude: 0.2,
            tWaveAmplitude: 0.3,
            prInterval: 160,
            qtInterval: 400
        };
        
        this.history = {
            heartRate: [],
            voltage: [],
            timestamps: []
        };
        
        this.isRecording = false;
        this.startTime = null;
        this.analysisCanvases = {};
        
        this.initializeAnalysis();
        this.startMeasurementLoop();
    }

    initializeAnalysis() {
        this.setupSpectrumAnalysis();
        this.setupHRVAnalysis();
        this.updateStatistics();
    }

    setupSpectrumAnalysis() {
        const canvas = document.getElementById('spectrumCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        this.analysisCanvases.spectrum = { canvas, ctx };
        
        // Draw frequency spectrum
        this.drawFrequencySpectrum();
    }

    setupHRVAnalysis() {
        const canvas = document.getElementById('hrvCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        this.analysisCanvases.hrv = { canvas, ctx };
        
        // Draw HRV analysis
        this.drawHRVAnalysis();
    }

    drawFrequencySpectrum() {
        const { canvas, ctx } = this.analysisCanvases.spectrum;
        if (!ctx) return;
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Background
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Grid
        ctx.strokeStyle = '#e9ecef';
        ctx.lineWidth = 1;
        
        for (let i = 0; i <= 10; i++) {
            const x = (canvas.width / 10) * i;
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvas.height);
            ctx.stroke();
            
            const y = (canvas.height / 10) * i;
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(canvas.width, y);
            ctx.stroke();
        }
        
        // Frequency spectrum data (simulated)
        const frequencies = [];
        const amplitudes = [];
        
        for (let f = 0; f <= 50; f += 0.5) {
            frequencies.push(f);
            let amplitude = 0;
            
            // QRS complex peak around 10-15 Hz
            if (f >= 8 && f <= 20) {
                amplitude = Math.exp(-Math.pow((f - 12) / 4, 2)) * 100;
            }
            
            // P and T wave components (lower frequencies)
            if (f >= 0.5 && f <= 5) {
                amplitude += Math.exp(-Math.pow((f - 2) / 1.5, 2)) * 30;
            }
            
            // Add some noise
            amplitude += Math.random() * 5;
            amplitudes.push(amplitude);
        }
        
        // Draw spectrum
        ctx.strokeStyle = '#007bff';
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        for (let i = 0; i < frequencies.length; i++) {
            const x = (frequencies[i] / 50) * canvas.width;
            const y = canvas.height - (amplitudes[i] / 100) * canvas.height;
            
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        
        ctx.stroke();
        
        // Labels
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.fillText('0 Hz', 5, canvas.height - 5);
        ctx.fillText('50 Hz', canvas.width - 35, canvas.height - 5);
        ctx.fillText('Amplitude', 5, 15);
    }

    drawHRVAnalysis() {
        const { canvas, ctx } = this.analysisCanvases.hrv;
        if (!ctx) return;
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Background
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Generate HRV data (Poincaré plot simulation)
        const rrIntervals = [];
        const rrNext = [];
        
        for (let i = 0; i < 50; i++) {
            const baseRR = 800; // ms
            const variation = (Math.random() - 0.5) * 100;
            const rr = baseRR + variation;
            rrIntervals.push(rr);
            
            if (i > 0) {
                rrNext.push(rr);
            }
        }
        
        // Draw Poincaré plot
        ctx.fillStyle = '#007bff';
        
        for (let i = 0; i < rrIntervals.length - 1; i++) {
            const x = ((rrIntervals[i] - 700) / 200) * canvas.width;
            const y = canvas.height - ((rrNext[i] - 700) / 200) * canvas.height;
            
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        // Draw identity line
        ctx.strokeStyle = '#dc3545';
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(0, canvas.height);
        ctx.lineTo(canvas.width, 0);
        ctx.stroke();
        ctx.setLineDash([]);
        
        // Labels
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.fillText('RR(n) ms', 5, canvas.height - 5);
        ctx.save();
        ctx.translate(15, canvas.height / 2);
        ctx.rotate(-Math.PI / 2);
        ctx.fillText('RR(n+1) ms', 0, 0);
        ctx.restore();
    }

    updateMeasurements() {
        // Simulate realistic ECG measurements with variations
        const time = Date.now();
        
        // Heart rate variation (60-100 BPM)
        const hrVariation = (Math.sin(time / 10000) + Math.random() - 0.5) * 10;
        this.measurements.heartRate = Math.max(60, Math.min(100, 75 + hrVariation));
        
        // QRS voltage variation
        const voltageVariation = (Math.random() - 0.5) * 0.3;
        this.measurements.qrsVoltage = Math.max(0.8, Math.min(2.0, 1.2 + voltageVariation));
        
        // R-R interval (inverse of heart rate)
        this.measurements.rrInterval = (60 / this.measurements.heartRate) * 1000;
        
        // Signal quality (85-100%)
        const qualityVariation = (Math.random() - 0.5) * 10;
        this.measurements.signalQuality = Math.max(85, Math.min(100, 95 + qualityVariation));
        
        // Store history
        this.history.heartRate.push(this.measurements.heartRate);
        this.history.voltage.push(this.measurements.qrsVoltage);
        this.history.timestamps.push(time);
        
        // Keep only last 100 measurements
        if (this.history.heartRate.length > 100) {
            this.history.heartRate.shift();
            this.history.voltage.shift();
            this.history.timestamps.shift();
        }
        
        this.updateDisplays();
        this.updateStatistics();
    }

    updateDisplays() {
        // Update measurement cards
        const elements = {
            'bpm-display': Math.round(this.measurements.heartRate),
            'heartRate': `${Math.round(this.measurements.heartRate)} BPM`,
            'qrs-voltage': this.measurements.qrsVoltage.toFixed(1),
            'voltage': `${this.measurements.qrsVoltage.toFixed(1)} mV`,
            'rr-interval': Math.round(this.measurements.rrInterval),
            'signal-quality': Math.round(this.measurements.signalQuality)
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                
                // Add animation class for value changes
                element.classList.add('value-updated');
                setTimeout(() => element.classList.remove('value-updated'), 300);
            }
        });
        
        // Update trend indicators
        this.updateTrends();
    }

    updateTrends() {
        const trends = {
            'bpm-display': this.getTrend(this.history.heartRate),
            'qrs-voltage': this.getTrend(this.history.voltage),
            'rr-interval': this.getTrend(this.history.heartRate.map(hr => 60000 / hr)),
            'signal-quality': 'stable'
        };
        
        Object.entries(trends).forEach(([id, trend]) => {
            const card = document.getElementById(id)?.closest('.measurement-card');
            if (card) {
                const trendElement = card.querySelector('.measurement-trend i');
                const trendText = card.querySelector('.measurement-trend span');
                
                if (trendElement && trendText) {
                    trendElement.className = `fas ${this.getTrendIcon(trend)} ${this.getTrendClass(trend)}`;
                    trendText.textContent = this.getTrendText(trend);
                }
            }
        });
    }

    getTrend(data) {
        if (data.length < 5) return 'stable';
        
        const recent = data.slice(-5);
        const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
        const older = data.slice(-10, -5);
        const oldAvg = older.reduce((a, b) => a + b, 0) / older.length;
        
        const change = (avg - oldAvg) / oldAvg;
        
        if (change > 0.05) return 'up';
        if (change < -0.05) return 'down';
        return 'stable';
    }

    getTrendIcon(trend) {
        switch (trend) {
            case 'up': return 'fa-arrow-up';
            case 'down': return 'fa-arrow-down';
            default: return 'fa-arrow-right';
        }
    }

    getTrendClass(trend) {
        switch (trend) {
            case 'up': return 'trend-up';
            case 'down': return 'trend-down';
            default: return 'trend-stable';
        }
    }

    getTrendText(trend) {
        switch (trend) {
            case 'up': return 'متزايد';
            case 'down': return 'متناقص';
            default: return 'مستقر';
        }
    }

    updateStatistics() {
        if (this.history.heartRate.length === 0) return;
        
        const avgHR = this.history.heartRate.reduce((a, b) => a + b, 0) / this.history.heartRate.length;
        const maxVoltage = Math.max(...this.history.voltage);
        const minVoltage = Math.min(...this.history.voltage);
        
        const elements = {
            'avgHeartRate': `${Math.round(avgHR)} BPM`,
            'maxVoltage': `${maxVoltage.toFixed(1)} mV`,
            'minVoltage': `${minVoltage.toFixed(1)} mV`,
            'recordingDuration': this.getRecordingDuration()
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    getRecordingDuration() {
        if (!this.startTime) {
            this.startTime = Date.now();
        }
        
        const duration = Date.now() - this.startTime;
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);
        
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    startMeasurementLoop() {
        this.isRecording = true;
        
        const updateInterval = setInterval(() => {
            if (!this.isRecording) {
                clearInterval(updateInterval);
                return;
            }
            
            this.updateMeasurements();
            
            // Update analysis charts every 5 seconds
            if (Math.floor(Date.now() / 5000) % 1 === 0) {
                this.drawFrequencySpectrum();
                this.drawHRVAnalysis();
            }
        }, 1000);
    }

    stopMeasurements() {
        this.isRecording = false;
    }

    exportData() {
        const data = {
            measurements: this.measurements,
            history: this.history,
            timestamp: new Date().toISOString(),
            duration: this.getRecordingDuration()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `ecg-measurements-${new Date().toISOString().slice(0, 10)}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    resetMeasurements() {
        this.history = {
            heartRate: [],
            voltage: [],
            timestamps: []
        };
        this.startTime = Date.now();
        this.updateStatistics();
    }
}

// Global functions
function exportMeasurements() {
    if (window.measurementSystem) {
        window.measurementSystem.exportData();
    }
}

function resetMeasurements() {
    if (window.measurementSystem) {
        window.measurementSystem.resetMeasurements();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.measurementSystem = new MeasurementSystem();
});
