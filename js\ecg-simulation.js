// ECG Simulation and Waveform Generation
class ECGSimulator {
    constructor() {
        this.isRunning = false;
        this.heartRate = 75; // BPM
        this.amplitude = 1.2; // mV
        this.noiseLevel = 0.05;
        this.time = 0;
        this.sampleRate = 500; // Hz
        this.waveformData = [];
        this.maxDataPoints = 2000;
        
        // ECG wave parameters
        this.pWave = { amplitude: 0.2, duration: 0.08 };
        this.qrsComplex = { amplitude: 1.0, duration: 0.08 };
        this.tWave = { amplitude: 0.3, duration: 0.16 };
        this.rrInterval = 60 / this.heartRate; // seconds
        
        this.initializeCanvases();

        // Start simulation after a short delay to ensure everything is ready
        setTimeout(() => {
            this.startSimulation();
        }, 100);
    }

    initializeCanvases() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeCanvases());
            return;
        }

        this.heroCanvas = document.getElementById('ecgCanvas');
        this.liveCanvas = document.getElementById('liveEcgCanvas');

        if (this.heroCanvas) {
            this.heroCtx = this.heroCanvas.getContext('2d');
            this.setupCanvas(this.heroCtx, this.heroCanvas);
            console.log('Hero ECG Canvas initialized');
        } else {
            console.warn('Hero ECG Canvas (ecgCanvas) not found');
        }

        if (this.liveCanvas) {
            this.liveCtx = this.liveCanvas.getContext('2d');
            this.setupCanvas(this.liveCtx, this.liveCanvas);
            console.log('Live ECG Canvas initialized');
        } else {
            console.warn('Live ECG Canvas (liveEcgCanvas) not found');
        }

        // Set main canvas reference for compatibility
        this.canvas = this.liveCanvas || this.heroCanvas;
        this.ctx = this.liveCtx || this.heroCtx;
    }

    setupCanvas(ctx, canvas) {
        if (!ctx || !canvas) {
            console.error('Invalid canvas or context');
            return;
        }

        try {
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            // Set up high DPI rendering
            const dpr = window.devicePixelRatio || 1;
            const rect = canvas.getBoundingClientRect();

            // Ensure we have valid dimensions
            const width = rect.width || canvas.width || 600;
            const height = rect.height || canvas.height || 300;

            canvas.width = width * dpr;
            canvas.height = height * dpr;
            ctx.scale(dpr, dpr);
            canvas.style.width = width + 'px';
            canvas.style.height = height + 'px';

            console.log(`Canvas setup complete: ${width}x${height}`);
        } catch (error) {
            console.error('Error setting up canvas:', error);
        }
    }

    generateECGPoint(t) {
        const cycleTime = t % this.rrInterval;
        const normalizedTime = cycleTime / this.rrInterval;
        
        let amplitude = 0;
        
        // P wave (0.0 - 0.15)
        if (normalizedTime >= 0.0 && normalizedTime <= 0.15) {
            const pTime = (normalizedTime - 0.075) / 0.075;
            amplitude += this.pWave.amplitude * Math.exp(-Math.pow(pTime * 3, 2));
        }
        
        // QRS complex (0.25 - 0.35)
        if (normalizedTime >= 0.25 && normalizedTime <= 0.35) {
            const qrsTime = (normalizedTime - 0.3) / 0.05;
            
            // Q wave (small negative)
            if (normalizedTime >= 0.25 && normalizedTime <= 0.27) {
                amplitude -= 0.1 * this.qrsComplex.amplitude;
            }
            // R wave (large positive)
            else if (normalizedTime >= 0.27 && normalizedTime <= 0.32) {
                const rTime = (normalizedTime - 0.295) / 0.025;
                amplitude += this.qrsComplex.amplitude * Math.exp(-Math.pow(rTime * 8, 2));
            }
            // S wave (negative)
            else if (normalizedTime >= 0.32 && normalizedTime <= 0.35) {
                amplitude -= 0.2 * this.qrsComplex.amplitude;
            }
        }
        
        // T wave (0.5 - 0.8)
        if (normalizedTime >= 0.5 && normalizedTime <= 0.8) {
            const tTime = (normalizedTime - 0.65) / 0.15;
            amplitude += this.tWave.amplitude * Math.exp(-Math.pow(tTime * 2, 2));
        }
        
        // Add noise
        amplitude += (Math.random() - 0.5) * this.noiseLevel;
        
        return amplitude * this.amplitude;
    }

    updateMeasurements() {
        // Update heart rate with slight variation
        const variation = (Math.random() - 0.5) * 5;
        this.heartRate = Math.max(60, Math.min(100, 75 + variation));
        this.rrInterval = 60 / this.heartRate;
        
        // Update display values
        document.getElementById('heartRate').textContent = `${Math.round(this.heartRate)} BPM`;
        document.getElementById('bpm-display').textContent = Math.round(this.heartRate);
        
        // Update voltage with variation
        const voltageVariation = (Math.random() - 0.5) * 0.3;
        const currentVoltage = 1.2 + voltageVariation;
        document.getElementById('voltage').textContent = `${currentVoltage.toFixed(1)} mV`;
        document.getElementById('qrs-voltage').textContent = currentVoltage.toFixed(1);
        
        // Update R-R interval
        const rrMs = this.rrInterval * 1000;
        document.getElementById('rr-interval').textContent = Math.round(rrMs);
        
        // Update signal quality
        const quality = 90 + Math.random() * 10;
        document.getElementById('signal-quality').textContent = Math.round(quality);
    }

    drawHeroECG() {
        if (!this.heroCtx) return;
        
        const canvas = this.heroCanvas;
        const ctx = this.heroCtx;
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw grid
        this.drawGrid(ctx, canvas);
        
        // Draw ECG waveform
        ctx.beginPath();
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 2;
        
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;
        const centerY = height / 2;
        const timeSpan = 3; // seconds
        
        for (let x = 0; x < width; x++) {
            const t = (x / width) * timeSpan + this.time;
            const amplitude = this.generateECGPoint(t);
            const y = centerY - (amplitude * height * 0.3);
            
            if (x === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        
        ctx.stroke();
    }

    drawLiveECG() {
        if (!this.liveCtx) return;
        
        const canvas = this.liveCanvas;
        const ctx = this.liveCtx;
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;
        
        // Add new data point
        const amplitude = this.generateECGPoint(this.time);
        this.waveformData.push(amplitude);
        
        // Keep only recent data
        if (this.waveformData.length > this.maxDataPoints) {
            this.waveformData.shift();
        }
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        // Draw grid
        this.drawGrid(ctx, canvas);
        
        // Draw waveform
        if (this.waveformData.length > 1) {
            ctx.beginPath();
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            
            const centerY = height / 2;
            const pointsPerPixel = this.waveformData.length / width;
            
            for (let x = 0; x < width; x++) {
                const dataIndex = Math.floor(x * pointsPerPixel);
                if (dataIndex < this.waveformData.length) {
                    const amplitude = this.waveformData[dataIndex];
                    const y = centerY - (amplitude * height * 0.3);
                    
                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
            }
            
            ctx.stroke();
        }
        
        // Draw sweep line
        const sweepX = (this.time * 100) % width;
        ctx.beginPath();
        ctx.strokeStyle = '#ffff00';
        ctx.lineWidth = 1;
        ctx.moveTo(sweepX, 0);
        ctx.lineTo(sweepX, height);
        ctx.stroke();
    }

    drawGrid(ctx, canvas) {
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;
        
        ctx.strokeStyle = 'rgba(0, 255, 0, 0.2)';
        ctx.lineWidth = 0.5;
        
        // Vertical lines
        for (let x = 0; x < width; x += 20) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }
        
        // Horizontal lines
        for (let y = 0; y < height; y += 20) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
        
        // Center line
        ctx.strokeStyle = 'rgba(0, 255, 0, 0.4)';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(0, height / 2);
        ctx.lineTo(width, height / 2);
        ctx.stroke();
    }

    startSimulation() {
        this.isRunning = true;
        this.animate();
    }

    stopSimulation() {
        this.isRunning = false;
    }

    animate() {
        if (!this.isRunning) return;
        
        this.time += 1 / 60; // 60 FPS
        
        this.drawHeroECG();
        this.drawLiveECG();
        
        // Update measurements every second
        if (Math.floor(this.time) % 1 === 0 && this.time % 1 < 0.02) {
            this.updateMeasurements();
        }
        
        requestAnimationFrame(() => this.animate());
    }

    pause() {
        this.isRunning = !this.isRunning;
        if (this.isRunning) {
            this.animate();
        }
    }

    reset() {
        this.time = 0;
        this.waveformData = [];
        this.heartRate = 75;
        this.amplitude = 1.2;
    }

    saveWaveform() {
        if (!this.liveCanvas) return;
        
        const link = document.createElement('a');
        link.download = `ecg-waveform-${new Date().toISOString().slice(0, 19)}.png`;
        link.href = this.liveCanvas.toDataURL();
        link.click();
    }
}



// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('Initializing ECG Simulator...');
        window.ecgSimulator = new ECGSimulator();
        console.log('ECG Simulator initialized successfully');
    } catch (error) {
        console.error('Error initializing ECG Simulator:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fee;
            border: 1px solid #fcc;
            color: #c33;
            padding: 15px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 300px;
        `;
        errorDiv.innerHTML = `
            <strong>خطأ في تحميل محاكي ECG</strong><br>
            يرجى إعادة تحميل الصفحة أو التحقق من وحدة التحكم للحصول على تفاصيل أكثر.
        `;
        document.body.appendChild(errorDiv);

        // Remove error message after 10 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 10000);
    }
});
