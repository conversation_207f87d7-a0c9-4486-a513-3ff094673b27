import React from 'react';

interface WaveformDisplayProps {
  type: string;
  title: string;
  description: string;
}

const WaveformDisplay: React.FC<WaveformDisplayProps> = ({ type, title, description }) => {
  const renderECGWaveform = () => (
    <svg viewBox="0 0 800 400" className="w-full h-auto border border-gray-300 rounded-lg bg-white">
      {/* Title */}
      <text x="400" y="30" textAnchor="middle" className="fill-gray-700 text-lg font-semibold">
        {title}
      </text>
      
      {/* Grid Lines */}
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" strokeWidth="1"/>
        </pattern>
      </defs>
      <rect x="50" y="50" width="700" height="300" fill="url(#grid)" />
      
      {/* Axes */}
      <line x1="50" y1="200" x2="750" y2="200" stroke="#333" strokeWidth="2" />
      <line x1="50" y1="50" x2="50" y2="350" stroke="#333" strokeWidth="2" />
      
      {/* Time axis labels */}
      <text x="50" y="370" textAnchor="middle" className="fill-gray-600 text-xs">0</text>
      <text x="150" y="370" textAnchor="middle" className="fill-gray-600 text-xs">0.2s</text>
      <text x="250" y="370" textAnchor="middle" className="fill-gray-600 text-xs">0.4s</text>
      <text x="350" y="370" textAnchor="middle" className="fill-gray-600 text-xs">0.6s</text>
      <text x="450" y="370" textAnchor="middle" className="fill-gray-600 text-xs">0.8s</text>
      <text x="550" y="370" textAnchor="middle" className="fill-gray-600 text-xs">1.0s</text>
      <text x="650" y="370" textAnchor="middle" className="fill-gray-600 text-xs">1.2s</text>
      <text x="750" y="370" textAnchor="middle" className="fill-gray-600 text-xs">1.4s</text>
      
      {/* Voltage axis labels */}
      <text x="40" y="110" textAnchor="end" className="fill-gray-600 text-xs">1mV</text>
      <text x="40" y="155" textAnchor="end" className="fill-gray-600 text-xs">0.5mV</text>
      <text x="40" y="205" textAnchor="end" className="fill-gray-600 text-xs">0</text>
      <text x="40" y="250" textAnchor="end" className="fill-gray-600 text-xs">-0.5mV</text>
      <text x="40" y="295" textAnchor="end" className="fill-gray-600 text-xs">-1mV</text>
      
      {/* ECG Waveform - First heartbeat */}
      <path d="M 50 200 L 80 200 L 90 180 L 100 200 L 110 200 L 120 220 L 130 120 L 140 280 L 150 200 L 160 200 L 170 170 L 180 200 L 200 200" 
            fill="none" stroke="#e74c3c" strokeWidth="3" />
      
      {/* ECG Waveform - Second heartbeat */}
      <path d="M 200 200 L 280 200 L 290 180 L 300 200 L 310 200 L 320 220 L 330 120 L 340 280 L 350 200 L 360 200 L 370 170 L 380 200 L 400 200" 
            fill="none" stroke="#e74c3c" strokeWidth="3" />
      
      {/* ECG Waveform - Third heartbeat */}
      <path d="M 400 200 L 480 200 L 490 180 L 500 200 L 510 200 L 520 220 L 530 120 L 540 280 L 550 200 L 560 200 L 570 170 L 580 200 L 600 200" 
            fill="none" stroke="#e74c3c" strokeWidth="3" />
      
      {/* ECG Waveform - Fourth heartbeat */}
      <path d="M 600 200 L 680 200 L 690 180 L 700 200 L 710 200 L 720 220 L 730 120 L 740 280 L 750 200" 
            fill="none" stroke="#e74c3c" strokeWidth="3" />
      
      {/* Labels for ECG components */}
      <text x="95" y="140" className="fill-blue-600 text-sm font-medium">P</text>
      <text x="135" y="90" className="fill-red-600 text-sm font-medium">QRS</text>
      <text x="175" y="140" className="fill-green-600 text-sm font-medium">T</text>
      
      {/* Interval markers */}
      <line x1="90" y1="330" x2="130" y2="330" stroke="#666" strokeWidth="1" strokeDasharray="3,3" />
      <text x="110" y="345" textAnchor="middle" className="fill-gray-600 text-xs">PR</text>
      
      <line x1="130" y1="320" x2="180" y2="320" stroke="#666" strokeWidth="1" strokeDasharray="3,3" />
      <text x="155" y="335" textAnchor="middle" className="fill-gray-600 text-xs">QT</text>
      
      <line x1="90" y1="310" x2="290" y2="310" stroke="#666" strokeWidth="1" strokeDasharray="3,3" />
      <text x="190" y="325" textAnchor="middle" className="fill-gray-600 text-xs">R-R Interval</text>
      
      {/* Heart rate calculation */}
      <text x="600" y="100" className="fill-gray-700 text-sm">Heart Rate: 72 bpm</text>
      <text x="600" y="120" className="fill-gray-700 text-sm">R-R Interval: 833 ms</text>
    </svg>
  );

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <p className="text-gray-700">{description}</p>
      </div>
      
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        {type === 'ecg' && renderECGWaveform()}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="bg-blue-50 p-3 rounded-lg">
          <span className="text-blue-700 font-medium">P Wave</span>
          <p className="text-blue-600 text-xs mt-1">Atrial depolarization</p>
        </div>
        <div className="bg-red-50 p-3 rounded-lg">
          <span className="text-red-700 font-medium">QRS Complex</span>
          <p className="text-red-600 text-xs mt-1">Ventricular depolarization</p>
        </div>
        <div className="bg-green-50 p-3 rounded-lg">
          <span className="text-green-700 font-medium">T Wave</span>
          <p className="text-green-600 text-xs mt-1">Ventricular repolarization</p>
        </div>
      </div>
    </div>
  );
};

export default WaveformDisplay;