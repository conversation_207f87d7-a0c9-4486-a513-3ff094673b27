import React from 'react';
import { Activity } from 'lucide-react';

const ENGExperiment = () => {
  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div className="flex items-center space-x-4 mb-6">
          <div className="p-3 bg-green-100 rounded-xl">
            <Activity className="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">ENG - Electroneurogram</h1>
            <p className="text-lg text-gray-600">Nerve conduction velocity measurement</p>
          </div>
        </div>
        
        <div className="bg-green-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-2">Coming Soon</h3>
          <p className="text-green-700">
            Complete ENG experiment documentation including SNAP recording, 
            nerve stimulation protocols, latency measurements, and conduction 
            velocity calculations will be available soon.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ENGExperiment;