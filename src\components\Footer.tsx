import React from 'react';
import { Github, Book, Mail } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white mt-16">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-xl font-semibold mb-4">BioMed Lab</h3>
            <p className="text-gray-400 mb-4">
              Comprehensive documentation for biomedical signal recording experiments and circuit designs.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Github className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Book className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Experiments</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="/ecg" className="hover:text-white transition-colors">ECG - Electrocardiogram</a></li>
              <li><a href="/emg" className="hover:text-white transition-colors">EMG - Electromyogram</a></li>
              <li><a href="/eeg" className="hover:text-white transition-colors">EEG - Electroencephalogram</a></li>
              <li><a href="/eog" className="hover:text-white transition-colors">EOG - Electrooculogram</a></li>
              <li><a href="/eng" className="hover:text-white transition-colors">ENG - Electroneurogram</a></li>
              <li><a href="/egg" className="hover:text-white transition-colors">EGG - Electrogastrogram</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Resources</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white transition-colors">Circuit Simulation</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Component Database</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Safety Guidelines</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Troubleshooting</a></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2025 BioMed Lab. All rights reserved. Educational use only.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;