import React from 'react';
import { Zap } from 'lucide-react';

const EMGExperiment = () => {
  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <div className="flex items-center space-x-4 mb-6">
          <div className="p-3 bg-orange-100 rounded-xl">
            <Zap className="h-8 w-8 text-orange-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">EMG - Electromyogram</h1>
            <p className="text-lg text-gray-600">Surface muscle electrical activity recording</p>
          </div>
        </div>
        
        <div className="bg-orange-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-orange-800 mb-2">Coming Soon</h3>
          <p className="text-orange-700">
            Complete EMG experiment documentation including surface electrode placement, 
            signal filtering for muscle activity, and RMS amplitude analysis across different 
            contraction levels will be available soon.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EMGExperiment;