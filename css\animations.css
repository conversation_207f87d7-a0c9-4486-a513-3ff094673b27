/* ECG Lab - Animations */

/* Keyframe Animations */
@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInDown {
    0% {
        transform: translateY(-30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(37, 99, 235, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(37, 99, 235, 0.8);
    }
}

@keyframes ecgWave {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes blinkingDot {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes rotateIn {
    0% {
        transform: rotate(-180deg);
        opacity: 0;
    }
    100% {
        transform: rotate(0deg);
        opacity: 1;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Animation Classes */
.animate-heartbeat {
    animation: heartbeat 1s ease-in-out infinite;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-slide-in-right {
    animation: slideInFromRight 0.8s ease-out;
}

.animate-slide-in-left {
    animation: slideInFromLeft 0.8s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.5s ease-out;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-rotate-in {
    animation: rotateIn 0.8s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

/* ECG Specific Animations */
.ecg-line {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: drawLine 2s ease-in-out forwards;
}

@keyframes drawLine {
    to {
        stroke-dashoffset: 0;
    }
}

.ecg-wave-moving {
    animation: ecgWave 3s linear infinite;
}

.status-indicator {
    position: relative;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    right: -15px;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    animation: blinkingDot 1s infinite;
}

/* Measurement Card Animations */
.measurement-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.measurement-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.measurement-value {
    transition: all 0.3s ease;
}

.measurement-card:hover .measurement-value {
    transform: scale(1.1);
    color: var(--secondary-color);
}

/* Circuit Diagram Animations */
.circuit-component {
    transition: all 0.3s ease;
    cursor: pointer;
}

.circuit-component:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 10px rgba(37, 99, 235, 0.6));
}

.circuit-wire {
    stroke-dasharray: 5, 5;
    animation: dashMove 1s linear infinite;
}

@keyframes dashMove {
    to {
        stroke-dashoffset: -10;
    }
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Hover Effects */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Navigation Animations */
.nav-link {
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: white;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Waveform Animation */
.waveform-line {
    position: relative;
    overflow: hidden;
}

.waveform-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background: var(--secondary-color);
    animation: sweepLine 3s linear infinite;
    box-shadow: 0 0 10px var(--secondary-color);
}

@keyframes sweepLine {
    0% { left: 0; }
    100% { left: 100%; }
}

/* Tab Animation */
.tab-btn {
    position: relative;
    transition: all 0.3s ease;
}

.tab-btn::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.tab-btn.active::before,
.tab-btn:hover::before {
    width: 100%;
}

/* Stagger Animation for Lists */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: staggerFadeIn 0.6s ease forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes staggerFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Animation Adjustments */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (max-width: 768px) {
    .measurement-card:hover {
        transform: translateY(-4px) scale(1.01);
    }
    
    .circuit-component:hover {
        transform: scale(1.05);
    }
}
