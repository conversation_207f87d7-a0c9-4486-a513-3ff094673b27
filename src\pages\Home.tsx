import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, Zap, Brain, Eye, Activity, Atom as <PERSON><PERSON><PERSON>, ArrowRight, Shield, BookOpen, Settings } from 'lucide-react';

const Home = () => {
  const experiments = [
    {
      id: 'ecg',
      name: 'ECG',
      fullName: 'Electrocardiogram',
      description: 'Record electrical activity of the heart with Lead I configuration',
      icon: Heart,
      color: 'bg-red-500',
      path: '/ecg',
      frequency: '0.5-40 Hz',
      amplitude: '0.1-3 mV'
    },
    {
      id: 'emg',
      name: 'EMG',
      fullName: 'Electromyogram',
      description: 'Surface recording of muscle electrical activity during contraction',
      icon: Zap,
      color: 'bg-orange-500',
      path: '/emg',
      frequency: '10-500 Hz',
      amplitude: '0.1-5 mV'
    },
    {
      id: 'eeg',
      name: 'EEG',
      fullName: 'Electroencephalogram',
      description: 'Single-channel brain activity recording with alpha rhythm detection',
      icon: Brain,
      color: 'bg-purple-500',
      path: '/eeg',
      frequency: '0.5-70 Hz',
      amplitude: '10-100 μV'
    },
    {
      id: 'eog',
      name: 'EOG',
      fullName: 'Electrooculogram',
      description: 'Dual-channel eye movement tracking (horizontal and vertical)',
      icon: Eye,
      color: 'bg-blue-500',
      path: '/eog',
      frequency: '0.01-30 Hz',
      amplitude: '10-200 μV'
    },
    {
      id: 'eng',
      name: 'ENG',
      fullName: 'Electroneurogram',
      description: 'Nerve conduction study with SNAP recording and velocity calculation',
      icon: Activity,
      color: 'bg-green-500',
      path: '/eng',
      frequency: '1-3000 Hz',
      amplitude: '1-50 μV'
    },
    {
      id: 'egg',
      name: 'EGG',
      fullName: 'Electrogastrogram',
      description: 'Gastric slow wave recording with respiratory artifact filtering',
      icon: Stomach,
      color: 'bg-teal-500',
      path: '/egg',
      frequency: '0.01-0.5 Hz',
      amplitude: '100-1000 μV'
    }
  ];

  const features = [
    {
      icon: Shield,
      title: 'Safety First',
      description: 'All circuits designed with patient safety and isolation in mind'
    },
    {
      icon: BookOpen,
      title: 'Complete Documentation',
      description: 'Detailed schematics, component lists, and measurement procedures'
    },
    {
      icon: Settings,
      title: 'Production Ready',
      description: 'Professional-grade circuits suitable for educational and research use'
    }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="text-center py-16 bg-gradient-to-r from-blue-600 to-teal-600 text-white rounded-3xl shadow-2xl">
        <div className="max-w-4xl mx-auto px-8">
          <h1 className="text-5xl font-bold mb-6">
            Biomedical Signal Recording
            <span className="block text-3xl font-normal mt-2 text-blue-100">
              Circuit Documentation
            </span>
          </h1>
          <p className="text-xl mb-8 text-blue-100 leading-relaxed">
            Complete documentation for six essential biomedical experiments including detailed circuit schematics, 
            component specifications, electrode placement guides, and expected waveforms.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/ecg"
              className="inline-flex items-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors shadow-lg"
            >
              Start with ECG
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <a
              href="#experiments"
              className="inline-flex items-center px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
            >
              View All Experiments
            </a>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose Our Documentation?</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Professional-grade documentation with attention to detail and educational value
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-xl mb-4">
                <feature.icon className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Experiments Grid */}
      <section id="experiments" className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Available Experiments</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Each experiment includes complete circuit schematics, component specifications, and measurement procedures
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {experiments.map((exp) => (
            <Link
              key={exp.id}
              to={exp.path}
              className="group bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-lg ${exp.color} text-white`}>
                    <exp.icon className="h-6 w-6" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {exp.name} - {exp.fullName}
                </h3>
                <p className="text-gray-600 mb-4">{exp.description}</p>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Frequency Range:</span>
                    <span className="font-medium">{exp.frequency}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Amplitude Range:</span>
                    <span className="font-medium">{exp.amplitude}</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* Technical Overview */}
      <section className="py-16 bg-gray-50 rounded-3xl">
        <div className="max-w-4xl mx-auto px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Technical Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Circuit Design</h3>
              <ul className="space-y-2 text-gray-600">
                <li>• AD623 instrumentation amplifier for high CMRR</li>
                <li>• MCP6002 dual op-amp for buffering and filtering</li>
                <li>• Single supply operation (5V or 3.3V)</li>
                <li>• Proper grounding and shielding techniques</li>
                <li>• Patient safety isolation considerations</li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Documentation Includes</h3>
              <ul className="space-y-2 text-gray-600">
                <li>• Complete circuit schematics with pin assignments</li>
                <li>• Component tables with part numbers and specifications</li>
                <li>• Electrode placement diagrams</li>
                <li>• Expected waveforms and measurement procedures</li>
                <li>• Troubleshooting guides and safety notes</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;