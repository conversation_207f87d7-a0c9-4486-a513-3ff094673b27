// Main Application Controller
class ECGLabApp {
    constructor() {
        this.isInitialized = false;
        this.currentSection = 'home';
        this.mobileMenuOpen = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupScrollAnimations();
        this.setupNavigation();
        this.setupResponsiveDesign();
        this.initializeComponents();
        
        this.isInitialized = true;
        console.log('ECG Lab Application initialized successfully');
    }

    setupEventListeners() {
        // Navigation menu toggle
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        if (hamburger && navMenu) {
            hamburger.addEventListener('click', () => {
                this.toggleMobileMenu();
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                this.scrollToSection(targetId);
                this.setActiveNavLink(link);
                
                // Close mobile menu if open
                if (this.mobileMenuOpen) {
                    this.toggleMobileMenu();
                }
            });
        });

        // Window resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Scroll handler for navigation highlighting
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    setupScrollAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        // Observe all elements with scroll-reveal class
        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });

        // Stagger animation for measurement cards
        const staggerObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const staggerItems = entry.target.querySelectorAll('.stagger-item');
                    staggerItems.forEach((item, index) => {
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }
            });
        }, observerOptions);

        const measurementsGrid = document.querySelector('.measurements-grid');
        if (measurementsGrid) {
            staggerObserver.observe(measurementsGrid);
        }
    }

    setupNavigation() {
        // Set initial active nav link
        const homeLink = document.querySelector('.nav-link[href="#home"]');
        if (homeLink) {
            this.setActiveNavLink(homeLink);
        }
    }

    setupResponsiveDesign() {
        // Handle responsive canvas sizing
        this.handleResize();
        
        // Setup responsive tables
        this.setupResponsiveTables();
    }

    setupResponsiveTables() {
        const tables = document.querySelectorAll('.component-table');
        tables.forEach(table => {
            if (!table.parentElement.classList.contains('table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });
    }

    initializeComponents() {
        // Add loading states
        this.showLoadingState();
        
        // Initialize after a short delay to ensure all modules are loaded
        setTimeout(() => {
            this.hideLoadingState();
            this.startAnimations();
        }, 1000);
    }

    toggleMobileMenu() {
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        if (hamburger && navMenu) {
            this.mobileMenuOpen = !this.mobileMenuOpen;
            
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
            
            // Animate hamburger
            const spans = hamburger.querySelectorAll('span');
            if (this.mobileMenuOpen) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                spans[0].style.transform = '';
                spans[1].style.opacity = '';
                spans[2].style.transform = '';
            }
        }
    }

    scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            const offsetTop = section.offsetTop - 80; // Account for fixed navbar
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
            this.currentSection = sectionId;
        }
    }

    setActiveNavLink(activeLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        activeLink.classList.add('active');
    }

    handleResize() {
        // Resize canvases
        if (window.ecgSimulator) {
            window.ecgSimulator.initializeCanvases();
        }
        
        // Close mobile menu on desktop
        if (window.innerWidth > 768 && this.mobileMenuOpen) {
            this.toggleMobileMenu();
        }
    }

    handleScroll() {
        const sections = ['home', 'measurements', 'circuits', 'analysis'];
        const scrollPosition = window.scrollY + 100;
        
        sections.forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;
                
                if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                    const navLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
                    if (navLink && !navLink.classList.contains('active')) {
                        this.setActiveNavLink(navLink);
                        this.currentSection = sectionId;
                    }
                }
            }
        });
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + key combinations
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 's':
                    e.preventDefault();
                    if (window.ecgSimulator) {
                        window.ecgSimulator.saveWaveform();
                    }
                    break;
                case 'r':
                    e.preventDefault();
                    if (window.ecgSimulator) {
                        window.ecgSimulator.reset();
                    }
                    break;
                case 'e':
                    e.preventDefault();
                    if (window.measurementSystem) {
                        window.measurementSystem.exportData();
                    }
                    break;
            }
        }
        
        // Space bar to pause/resume
        if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            if (window.ecgSimulator) {
                window.ecgSimulator.pause();
            }
        }
    }

    showLoadingState() {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>جاري تحميل مختبر ECG...</p>
            </div>
        `;
        loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            font-family: 'Tajawal', sans-serif;
        `;
        
        document.body.appendChild(loadingOverlay);
    }

    hideLoadingState() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.remove();
            }, 300);
        }
    }

    startAnimations() {
        // Start heartbeat animation on logo
        const logo = document.querySelector('.nav-logo i');
        if (logo) {
            logo.classList.add('animate-heartbeat');
        }
        
        // Start ECG monitor glow
        const monitor = document.querySelector('.ecg-monitor');
        if (monitor) {
            monitor.classList.add('animate-glow');
        }
        
        // Animate hero content
        const heroContent = document.querySelector('.hero-content');
        const heroVisual = document.querySelector('.hero-visual');
        
        if (heroContent) {
            heroContent.classList.add('animate-slide-in-right');
        }
        
        if (heroVisual) {
            heroVisual.classList.add('animate-slide-in-left');
        }
    }

    // Utility methods
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 1000;
            animation: slideInFromRight 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutToRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    getCurrentSection() {
        return this.currentSection;
    }

    isAppInitialized() {
        return this.isInitialized;
    }
}

// Global utility functions
function showNotification(message, type = 'info') {
    if (window.ecgLabApp) {
        window.ecgLabApp.showNotification(message, type);
    }
}

function getCurrentSection() {
    return window.ecgLabApp ? window.ecgLabApp.getCurrentSection() : 'home';
}

// Global functions for HTML onclick events
function startMeasurement() {
    if (window.ecgSimulator) {
        window.ecgSimulator.startSimulation();
        showNotification('تم بدء القياس بنجاح', 'success');
    }
    // Scroll to measurements section
    document.getElementById('measurements').scrollIntoView({ behavior: 'smooth' });
}

function viewCircuits() {
    document.getElementById('circuits').scrollIntoView({ behavior: 'smooth' });
}

function pauseWaveform() {
    if (window.ecgSimulator) {
        if (window.ecgSimulator.isRunning) {
            window.ecgSimulator.pause();
            showNotification('تم إيقاف الموجة مؤقتاً', 'info');
        } else {
            window.ecgSimulator.startSimulation();
            showNotification('تم استئناف الموجة', 'success');
        }
    }
}

function resetWaveform() {
    if (window.ecgSimulator) {
        window.ecgSimulator.reset();
        showNotification('تم إعادة تعيين الموجة', 'info');
    }
}

function saveWaveform() {
    if (window.ecgSimulator) {
        window.ecgSimulator.saveWaveform();
        showNotification('تم حفظ الموجة بنجاح', 'success');
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.ecgLabApp = new ECGLabApp();
    
    // Add CSS for notifications
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInFromRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOutToRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        .value-updated {
            animation: valueUpdate 0.3s ease-out;
        }
        
        @keyframes valueUpdate {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); color: var(--secondary-color); }
            100% { transform: scale(1); }
        }
        
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .loading-content {
            text-align: center;
            color: #333;
        }
        
        .loading-content p {
            margin-top: 20px;
            font-size: 18px;
        }
    `;
    document.head.appendChild(style);
});

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Pause animations when page is not visible
        if (window.ecgSimulator) {
            window.ecgSimulator.stopSimulation();
        }
    } else {
        // Resume animations when page becomes visible
        if (window.ecgSimulator) {
            window.ecgSimulator.startSimulation();
        }
    }
});
