import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Activity, Zap } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const experiments = [
    { id: 'ecg', name: 'ECG', path: '/ecg', description: 'Electrocardiogram' },
    { id: 'emg', name: 'EMG', path: '/emg', description: 'Electromyogram' },
    { id: 'eeg', name: 'EEG', path: '/eeg', description: 'Electroencephalogram' },
    { id: 'eog', name: 'EOG', path: '/eog', description: 'Electrooculogram' },
    { id: 'eng', name: 'ENG', path: '/eng', description: 'Electroneurogram' },
    { id: 'egg', name: 'EG<PERSON>', path: '/egg', description: 'Electrogastrogram' }
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="bg-white shadow-lg border-b-2 border-blue-100">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <Activity className="h-8 w-8 text-blue-600 transition-transform group-hover:scale-110" />
              <Zap className="h-4 w-4 text-teal-500 absolute -top-1 -right-1" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">BioMed Lab</h1>
              <p className="text-sm text-gray-600">Circuit Documentation</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex space-x-1">
            <Link 
              to="/" 
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                isActive('/') ? 'bg-blue-600 text-white' : 'text-gray-700 hover:bg-blue-50'
              }`}
            >
              Home
            </Link>
            {experiments.map((exp) => (
              <Link
                key={exp.id}
                to={exp.path}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  isActive(exp.path) ? 'bg-blue-600 text-white' : 'text-gray-700 hover:bg-blue-50'
                }`}
                title={exp.description}
              >
                {exp.name}
              </Link>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t border-gray-200">
            <div className="space-y-2">
              <Link 
                to="/" 
                className={`block px-4 py-3 rounded-lg font-medium transition-colors ${
                  isActive('/') ? 'bg-blue-600 text-white' : 'text-gray-700 hover:bg-blue-50'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>
              {experiments.map((exp) => (
                <Link
                  key={exp.id}
                  to={exp.path}
                  className={`block px-4 py-3 rounded-lg font-medium transition-colors ${
                    isActive(exp.path) ? 'bg-blue-600 text-white' : 'text-gray-700 hover:bg-blue-50'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div>
                    <span className="font-semibold">{exp.name}</span>
                    <span className="block text-sm text-gray-500">{exp.description}</span>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;