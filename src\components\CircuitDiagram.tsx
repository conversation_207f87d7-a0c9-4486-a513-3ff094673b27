import React from 'react';

interface CircuitDiagramProps {
  type: string;
  title: string;
  description: string;
}

const CircuitDiagram: React.FC<CircuitDiagramProps> = ({ type, title, description }) => {
  const renderECGCircuit = () => (
    <svg viewBox="0 0 800 600" className="w-full h-auto border border-gray-300 rounded-lg bg-white">
      {/* Title */}
      <text x="400" y="30" textAnchor="middle" className="fill-gray-700 text-lg font-semibold">
        {title}
      </text>
      
      {/* Power Supply */}
      <rect x="50" y="60" width="60" height="40" fill="none" stroke="#333" strokeWidth="2" />
      <text x="80" y="85" textAnchor="middle" className="fill-gray-700 text-sm">+5V</text>
      
      {/* Ground */}
      <line x1="50" y1="120" x2="110" y2="120" stroke="#333" strokeWidth="2" />
      <line x1="70" y1="120" x2="70" y2="130" stroke="#333" strokeWidth="2" />
      <line x1="80" y1="120" x2="80" y2="130" stroke="#333" strokeWidth="2" />
      <line x1="90" y1="120" x2="90" y2="130" stroke="#333" strokeWidth="2" />
      <text x="80" y="145" textAnchor="middle" className="fill-gray-700 text-sm">GND</text>
      
      {/* Voltage Divider (R1, R2) */}
      <rect x="150" y="70" width="20" height="60" fill="none" stroke="#333" strokeWidth="2" />
      <text x="180" y="85" className="fill-gray-700 text-xs">R1</text>
      <text x="180" y="100" className="fill-gray-700 text-xs">10kΩ</text>
      
      <rect x="150" y="140" width="20" height="60" fill="none" stroke="#333" strokeWidth="2" />
      <text x="180" y="155" className="fill-gray-700 text-xs">R2</text>
      <text x="180" y="170" className="fill-gray-700 text-xs">10kΩ</text>
      
      {/* Voltage Follower (U2A) */}
      <polygon points="220,130 220,170 270,150" fill="none" stroke="#333" strokeWidth="2" />
      <text x="240" y="145" textAnchor="middle" className="fill-gray-700 text-xs">U2A</text>
      <text x="240" y="190" textAnchor="middle" className="fill-gray-700 text-xs">MCP6002</text>
      
      {/* Vref Output */}
      <circle cx="300" cy="150" r="3" fill="#333" />
      <text x="310" y="155" className="fill-gray-700 text-sm">Vref</text>
      
      {/* Input Electrodes */}
      <circle cx="100" cy="250" r="8" fill="#e74c3c" />
      <text x="110" y="255" className="fill-gray-700 text-sm">LA</text>
      
      <circle cx="100" cy="300" r="8" fill="#3498db" />
      <text x="110" y="305" className="fill-gray-700 text-sm">RA</text>
      
      <circle cx="100" cy="350" r="8" fill="#2ecc71" />
      <text x="110" y="355" className="fill-gray-700 text-sm">RL</text>
      
      {/* AD623 Instrumentation Amplifier */}
      <rect x="200" y="270" width="120" height="80" fill="none" stroke="#333" strokeWidth="2" />
      <text x="260" y="290" textAnchor="middle" className="fill-gray-700 text-sm font-semibold">AD623</text>
      <text x="260" y="305" textAnchor="middle" className="fill-gray-700 text-xs">Instrumentation</text>
      <text x="260" y="318" textAnchor="middle" className="fill-gray-700 text-xs">Amplifier</text>
      
      {/* AD623 Pins */}
      <text x="190" y="290" className="fill-gray-700 text-xs">+IN</text>
      <text x="190" y="305" className="fill-gray-700 text-xs">-IN</text>
      <text x="190" y="320" className="fill-gray-700 text-xs">REF</text>
      <text x="330" y="290" className="fill-gray-700 text-xs">OUT</text>
      <text x="330" y="305" className="fill-gray-700 text-xs">+Vs</text>
      <text x="330" y="320" className="fill-gray-700 text-xs">-Vs</text>
      
      {/* Gain Setting Resistor */}
      <rect x="235" y="330" width="50" height="15" fill="none" stroke="#333" strokeWidth="2" />
      <text x="260" y="340" textAnchor="middle" className="fill-gray-700 text-xs">Rg = 100Ω</text>
      
      {/* High Pass Filter */}
      <rect x="400" y="280" width="20" height="40" fill="none" stroke="#333" strokeWidth="2" />
      <text x="390" y="295" className="fill-gray-700 text-xs">C_hpf</text>
      <text x="390" y="308" className="fill-gray-700 text-xs">0.33μF</text>
      
      <rect x="450" y="285" width="20" height="30" fill="none" stroke="#333" strokeWidth="2" />
      <text x="480" y="295" className="fill-gray-700 text-xs">R_hpf</text>
      <text x="480" y="308" className="fill-gray-700 text-xs">1MΩ</text>
      
      {/* Low Pass Filter */}
      <rect x="520" y="285" width="20" height="30" fill="none" stroke="#333" strokeWidth="2" />
      <text x="550" y="295" className="fill-gray-700 text-xs">R_lpf</text>
      <text x="550" y="308" className="fill-gray-700 text-xs">4kΩ</text>
      
      <rect x="570" y="280" width="20" height="40" fill="none" stroke="#333" strokeWidth="2" />
      <text x="600" y="295" className="fill-gray-700 text-xs">C_lpf</text>
      <text x="600" y="308" className="fill-gray-700 text-xs">1μF</text>
      
      {/* Output */}
      <circle cx="650" cy="300" r="5" fill="#f39c12" />
      <text x="660" y="305" className="fill-gray-700 text-sm">ECG Out</text>
      
      {/* Connection Lines */}
      <line x1="110" y1="80" x2="150" y2="80" stroke="#333" strokeWidth="2" />
      <line x1="160" y1="100" x2="160" y2="130" stroke="#333" strokeWidth="2" />
      <line x1="160" y1="130" x2="160" y2="140" stroke="#333" strokeWidth="2" />
      <line x1="160" y1="140" x2="160" y2="200" stroke="#333" strokeWidth="2" />
      <line x1="80" y1="120" x2="160" y2="120" stroke="#333" strokeWidth="2" />
      <line x1="160" y1="200" x2="160" y2="200" stroke="#333" strokeWidth="2" />
      
      <line x1="170" y1="130" x2="220" y2="130" stroke="#333" strokeWidth="2" />
      <line x1="270" y1="150" x2="300" y2="150" stroke="#333" strokeWidth="2" />
      
      <line x1="108" y1="250" x2="200" y2="250" stroke="#e74c3c" strokeWidth="2" />
      <line x1="200" y1="250" x2="200" y2="285" stroke="#e74c3c" strokeWidth="2" />
      
      <line x1="108" y1="300" x2="200" y2="300" stroke="#3498db" strokeWidth="2" />
      
      <line x1="108" y1="350" x2="200" y2="350" stroke="#2ecc71" strokeWidth="2" />
      <line x1="200" y1="350" x2="200" y2="315" stroke="#2ecc71" strokeWidth="2" />
      
      <line x1="320" y1="285" x2="400" y2="285" stroke="#333" strokeWidth="2" />
      <line x1="420" y1="300" x2="450" y2="300" stroke="#333" strokeWidth="2" />
      <line x1="470" y1="300" x2="520" y2="300" stroke="#333" strokeWidth="2" />
      <line x1="540" y1="300" x2="570" y2="300" stroke="#333" strokeWidth="2" />
      <line x1="590" y1="300" x2="650" y2="300" stroke="#333" strokeWidth="2" />
      
      {/* Frequency Response Labels */}
      <text x="430" y="360" className="fill-gray-600 text-xs">HPF: 0.5Hz</text>
      <text x="550" y="360" className="fill-gray-600 text-xs">LPF: 40Hz</text>
      <text x="260" y="380" className="fill-gray-600 text-xs">Gain: 500</text>
    </svg>
  );

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <p className="text-gray-700">{description}</p>
      </div>
      
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        {type === 'ecg' && renderECGCircuit()}
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div className="bg-red-50 p-3 rounded-lg">
          <div className="w-4 h-4 bg-red-500 rounded-full mb-2"></div>
          <span className="text-red-700 font-medium">Positive Input (LA)</span>
        </div>
        <div className="bg-blue-50 p-3 rounded-lg">
          <div className="w-4 h-4 bg-blue-500 rounded-full mb-2"></div>
          <span className="text-blue-700 font-medium">Negative Input (RA)</span>
        </div>
        <div className="bg-green-50 p-3 rounded-lg">
          <div className="w-4 h-4 bg-green-500 rounded-full mb-2"></div>
          <span className="text-green-700 font-medium">Reference (RL)</span>
        </div>
        <div className="bg-orange-50 p-3 rounded-lg">
          <div className="w-4 h-4 bg-orange-500 rounded-full mb-2"></div>
          <span className="text-orange-700 font-medium">Signal Output</span>
        </div>
      </div>
    </div>
  );
};

export default CircuitDiagram;